<!--components/checklist/checklist.wxml-->
<view class="checklist-container">
  <view class="checklist-header" wx:if="{{title}}">
    <text class="checklist-title">{{title}}</text>
    <text class="checklist-subtitle" wx:if="{{subtitle}}">{{subtitle}}</text>
  </view>

  <view class="checklist-items">
    <view class="checklist-item" wx:for="{{items}}" wx:key="index" data-index="{{index}}" bindtap="onItemTap">
      <view class="checkbox {{status[index] ? 'checked' : ''}}">
        <text class="checkbox-icon" wx:if="{{status[index]}}">✓</text>
      </view>
      <view class="item-content">
        <text class="item-text {{status[index] ? 'completed' : ''}}">{{item.text || item}}</text>
        <text class="item-desc" wx:if="{{item.description}}">{{item.description}}</text>
      </view>
      <view class="item-status" wx:if="{{showStatus}}">
        <text class="status-icon" wx:if="{{status[index]}}">✅</text>
      </view>
    </view>
  </view>

  <view class="checklist-progress" wx:if="{{showProgress}}">
    <view class="progress-info">
      <text class="progress-text">完成进度：{{completedCount}}/{{totalCount}}</text>
      <text class="progress-percent">{{progressPercent}}%</text>
    </view>
    <view class="progress-bar">
      <view class="progress-track">
        <view class="progress-fill" style="width: {{progressPercent}}%"></view>
      </view>
    </view>
  </view>

  <view class="checklist-actions" wx:if="{{showActions}}">
    <button class="action-btn secondary" bindtap="onClearAll" wx:if="{{completedCount > 0}}">
      <text class="btn-icon">🗑️</text>
      <text class="btn-text">清空</text>
    </button>
    <button class="action-btn primary" bindtap="onCompleteAll" wx:if="{{completedCount < totalCount}}">
      <text class="btn-icon">✅</text>
      <text class="btn-text">全选</text>
    </button>
  </view>
</view>
