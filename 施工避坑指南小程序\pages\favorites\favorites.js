// pages/favorites/favorites.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    favoritesList: [],
    filteredFavorites: [],
    searchKeyword: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadFavorites()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadFavorites()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadFavorites()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的收藏 - 施工避坑指南',
      path: '/pages/favorites/favorites',
      imageUrl: '/images/share-cover.jpg'
    }
  },

  // 加载收藏列表
  loadFavorites() {
    try {
      const favorites = wx.getStorageSync('favorites') || []
      this.setData({
        favoritesList: favorites,
        filteredFavorites: favorites
      })
    } catch (error) {
      console.error('加载收藏列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    this.filterFavorites(keyword)
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: '',
      filteredFavorites: this.data.favoritesList
    })
  },

  // 过滤收藏列表
  filterFavorites(keyword) {
    if (!keyword) {
      this.setData({
        filteredFavorites: this.data.favoritesList
      })
      return
    }

    const filtered = this.data.favoritesList.filter(item => {
      return item.title.includes(keyword) ||
             item.description.includes(keyword) ||
             item.category.includes(keyword)
    })

    this.setData({
      filteredFavorites: filtered
    })
  },

  // 移除收藏
  removeFavorite(e) {
    const episodeId = e.currentTarget.dataset.episode

    wx.showModal({
      title: '确认删除',
      content: '确定要从收藏中移除这个内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.doRemoveFavorite(episodeId)
        }
      }
    })
  },

  // 执行移除收藏
  doRemoveFavorite(episodeId) {
    try {
      // 使用app的removeFavorite方法
      const success = app.removeFavorite(episodeId)

      if (success) {
        // 重新加载收藏列表
        this.loadFavorites()

        wx.showToast({
          title: '已移除',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: '移除失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('移除收藏失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      })
    }
  },

  // 导航到期数详情
  navigateToEpisode(e) {
    const episodeId = e.currentTarget.dataset.episode
    wx.navigateTo({
      url: `/pages/episode/episode?episodeId=${episodeId}`
    })
  },

  // 去首页
  goToIndex() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})