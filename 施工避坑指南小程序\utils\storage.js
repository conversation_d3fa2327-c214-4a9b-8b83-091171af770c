// utils/storage.js
// 本地存储工具类

class Storage {
  /**
   * 设置存储数据
   * @param {string} key 存储键
   * @param {any} data 存储数据
   * @returns {boolean} 是否成功
   */
  static set(key, data) {
    try {
      wx.setStorageSync(key, data)
      return true
    } catch (error) {
      console.error('存储数据失败:', error)
      return false
    }
  }

  /**
   * 获取存储数据
   * @param {string} key 存储键
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的数据
   */
  static get(key, defaultValue = null) {
    try {
      const data = wx.getStorageSync(key)
      return data !== '' ? data : defaultValue
    } catch (error) {
      console.error('获取存储数据失败:', error)
      return defaultValue
    }
  }

  /**
   * 删除存储数据
   * @param {string} key 存储键
   * @returns {boolean} 是否成功
   */
  static remove(key) {
    try {
      wx.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('删除存储数据失败:', error)
      return false
    }
  }

  /**
   * 清空所有存储数据
   * @returns {boolean} 是否成功
   */
  static clear() {
    try {
      wx.clearStorageSync()
      return true
    } catch (error) {
      console.error('清空存储数据失败:', error)
      return false
    }
  }

  /**
   * 获取存储信息
   * @returns {object} 存储信息
   */
  static getInfo() {
    try {
      return wx.getStorageInfoSync()
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return {
        keys: [],
        currentSize: 0,
        limitSize: 0
      }
    }
  }
}

// 学习进度管理
class LearningProgress {
  static KEY = 'learningProgress'

  /**
   * 获取学习进度
   * @param {number} episodeId 期数ID
   * @returns {number} 进度百分比
   */
  static get(episodeId) {
    const allProgress = Storage.get(this.KEY, {})
    return allProgress[episodeId] || 0
  }

  /**
   * 设置学习进度
   * @param {number} episodeId 期数ID
   * @param {number} progress 进度百分比
   * @returns {boolean} 是否成功
   */
  static set(episodeId, progress) {
    const allProgress = Storage.get(this.KEY, {})
    allProgress[episodeId] = Math.max(0, Math.min(100, progress))
    return Storage.set(this.KEY, allProgress)
  }

  /**
   * 获取所有学习进度
   * @returns {object} 所有进度数据
   */
  static getAll() {
    return Storage.get(this.KEY, {})
  }

  /**
   * 清空学习进度
   * @returns {boolean} 是否成功
   */
  static clear() {
    return Storage.set(this.KEY, {})
  }
}

// 收藏管理
class Favorites {
  static KEY = 'favorites'

  /**
   * 获取收藏列表
   * @returns {array} 收藏的期数ID列表
   */
  static getList() {
    return Storage.get(this.KEY, [])
  }

  /**
   * 添加收藏
   * @param {number} episodeId 期数ID
   * @returns {boolean} 是否成功
   */
  static add(episodeId) {
    const favorites = this.getList()
    if (!favorites.includes(episodeId)) {
      favorites.push(episodeId)
      return Storage.set(this.KEY, favorites)
    }
    return false
  }

  /**
   * 移除收藏
   * @param {number} episodeId 期数ID
   * @returns {boolean} 是否成功
   */
  static remove(episodeId) {
    const favorites = this.getList()
    const index = favorites.indexOf(episodeId)
    if (index > -1) {
      favorites.splice(index, 1)
      return Storage.set(this.KEY, favorites)
    }
    return false
  }

  /**
   * 检查是否已收藏
   * @param {number} episodeId 期数ID
   * @returns {boolean} 是否已收藏
   */
  static has(episodeId) {
    return this.getList().includes(episodeId)
  }

  /**
   * 清空收藏
   * @returns {boolean} 是否成功
   */
  static clear() {
    return Storage.set(this.KEY, [])
  }
}

// 检查清单状态管理
class ChecklistStatus {
  static KEY = 'checklistStatus'

  /**
   * 获取检查清单状态
   * @param {number} episodeId 期数ID
   * @returns {object} 检查清单状态
   */
  static get(episodeId) {
    const allStatus = Storage.get(this.KEY, {})
    return allStatus[episodeId] || {}
  }

  /**
   * 设置检查清单状态
   * @param {number} episodeId 期数ID
   * @param {object} status 检查清单状态
   * @returns {boolean} 是否成功
   */
  static set(episodeId, status) {
    const allStatus = Storage.get(this.KEY, {})
    allStatus[episodeId] = status
    return Storage.set(this.KEY, allStatus)
  }

  /**
   * 设置单个检查项状态
   * @param {number} episodeId 期数ID
   * @param {number} itemIndex 检查项索引
   * @param {boolean} checked 是否完成
   * @returns {boolean} 是否成功
   */
  static setItem(episodeId, itemIndex, checked) {
    const status = this.get(episodeId)
    status[itemIndex] = checked
    return this.set(episodeId, status)
  }

  /**
   * 获取完成进度
   * @param {number} episodeId 期数ID
   * @param {number} totalItems 总检查项数量
   * @returns {object} 进度信息
   */
  static getProgress(episodeId, totalItems) {
    const status = this.get(episodeId)
    const completedCount = Object.values(status).filter(checked => checked).length
    const progress = totalItems > 0 ? Math.round((completedCount / totalItems) * 100) : 0
    
    return {
      completedCount,
      totalItems,
      progress
    }
  }

  /**
   * 获取所有检查清单状态
   * @returns {object} 所有状态数据
   */
  static getAll() {
    return Storage.get(this.KEY, {})
  }

  /**
   * 清空检查清单状态
   * @returns {boolean} 是否成功
   */
  static clear() {
    return Storage.set(this.KEY, {})
  }
}

// 用户设置管理
class UserSettings {
  static KEY = 'userSettings'

  /**
   * 获取默认设置
   * @returns {object} 默认设置
   */
  static getDefaults() {
    return {
      theme: 'light', // 主题：light/dark
      fontSize: 'medium', // 字体大小：small/medium/large
      autoSave: true, // 自动保存进度
      vibration: true, // 触觉反馈
      sound: true, // 声音反馈
      notifications: true // 通知提醒
    }
  }

  /**
   * 获取用户设置
   * @returns {object} 用户设置
   */
  static get() {
    const defaults = this.getDefaults()
    const settings = Storage.get(this.KEY, {})
    return { ...defaults, ...settings }
  }

  /**
   * 设置用户配置
   * @param {object} settings 设置对象
   * @returns {boolean} 是否成功
   */
  static set(settings) {
    const currentSettings = this.get()
    const newSettings = { ...currentSettings, ...settings }
    return Storage.set(this.KEY, newSettings)
  }

  /**
   * 获取单个设置项
   * @param {string} key 设置键
   * @returns {any} 设置值
   */
  static getItem(key) {
    const settings = this.get()
    return settings[key]
  }

  /**
   * 设置单个配置项
   * @param {string} key 设置键
   * @param {any} value 设置值
   * @returns {boolean} 是否成功
   */
  static setItem(key, value) {
    const settings = this.get()
    settings[key] = value
    return Storage.set(this.KEY, settings)
  }

  /**
   * 重置为默认设置
   * @returns {boolean} 是否成功
   */
  static reset() {
    return Storage.set(this.KEY, this.getDefaults())
  }
}

module.exports = {
  Storage,
  LearningProgress,
  Favorites,
  ChecklistStatus,
  UserSettings
}
