#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为所有episode页面添加返回首页的按钮
"""

import os
import re

def add_home_button_to_episode(file_path):
    """为单个episode文件添加首页按钮"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找episode-nav区域并添加首页按钮
        # 匹配模式：<div class="episode-nav">...上一期...期数...下一期...</div>
        pattern = r'(<div class="episode-nav">\s*<a href=\'[^\']*\' class=\'nav-link\'>← 上一期</a>\s*<span class="episode-indicator">\d+/64</span>\s*)(<a href=\'[^\']*\' class=\'nav-link\'>下一期 →</a>\s*</div>)'
        
        # 替换：在期数指示器和下一期按钮之间插入首页按钮
        replacement = r'\1<a href=\'../index.html\' class=\'nav-link home-link\'>🏠 首页</a>\n                \2'
        
        new_content = re.sub(pattern, replacement, content)
        
        # 如果没有匹配到，尝试其他模式（可能是第一期或最后一期）
        if new_content == content:
            # 尝试匹配只有下一期的情况（第一期）
            pattern_first = r'(<div class="episode-nav">\s*<span class="episode-indicator">1/64</span>\s*)(<a href=\'[^\']*\' class=\'nav-link\'>下一期 →</a>\s*</div>)'
            new_content = re.sub(pattern_first, r'\1<a href=\'../index.html\' class=\'nav-link home-link\'>🏠 首页</a>\n                \2', content)
            
        # 如果还没有匹配到，尝试匹配只有上一期的情况（最后一期）
        if new_content == content:
            pattern_last = r'(<div class="episode-nav">\s*<a href=\'[^\']*\' class=\'nav-link\'>← 上一期</a>\s*<span class="episode-indicator">64/64</span>\s*)(</div>)'
            new_content = re.sub(pattern_last, r'\1<a href=\'../index.html\' class=\'nav-link home-link\'>🏠 首页</a>\n                \2', content)
        
        # 如果内容有变化，写回文件
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"Success: {os.path.basename(file_path)} - home button added")
            return True
        else:
            print(f"Warning: {os.path.basename(file_path)} - no matching nav structure found")
            return False

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """主函数"""
    episodes_dir = "episodes"
    
    if not os.path.exists(episodes_dir):
        print(f"Error: Directory {episodes_dir} does not exist")
        return
    
    success_count = 0
    total_count = 0
    
    # 遍历所有episode文件
    for filename in sorted(os.listdir(episodes_dir)):
        if filename.startswith('episode-') and filename.endswith('.html'):
            file_path = os.path.join(episodes_dir, filename)
            total_count += 1
            
            if add_home_button_to_episode(file_path):
                success_count += 1
    
    print(f"\nProcessing complete: {success_count}/{total_count} files successfully added home button")

if __name__ == "__main__":
    main()
