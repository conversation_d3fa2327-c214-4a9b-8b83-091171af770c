<!--pages/progress/progress.wxml-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="logo">
      <text class="title">📊 装修进度</text>
      <text class="subtitle">跟踪您的装修进展</text>
    </view>
    <view class="progress-stats">
      <view class="progress-stat">
        <text class="stat-number">{{overallProgress}}%</text>
        <text class="stat-label">总体进度</text>
      </view>
      <view class="progress-stat">
        <text class="stat-number">{{completedEpisodes}}</text>
        <text class="stat-label">已完成期数</text>
      </view>
    </view>
  </view>

  <!-- 当前阶段 -->
  <view class="current-phase-section">
    <text class="section-title">🚧 当前阶段</text>
    <view class="current-phase-card card">
      <view class="phase-header">
        <text class="phase-icon">{{currentPhase.icon}}</text>
        <view class="phase-info">
          <text class="phase-title">{{currentPhase.title}}</text>
          <text class="phase-desc">第{{currentPhase.startEpisode}}-{{currentPhase.endEpisode}}期</text>
        </view>
        <view class="phase-progress">
          <text class="progress-text">{{currentPhase.completedCount}}/{{currentPhase.totalCount}}</text>
        </view>
      </view>
      <view class="progress-bar">
        <view class="progress-track">
          <view class="progress-fill" style="width: {{currentPhase.progressPercent}}%"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 阶段进度列表 -->
  <view class="phases-progress-section">
    <text class="section-title">📋 各阶段进度</text>
    <view class="phases-list">
      <view class="phase-progress-item" wx:for="{{phasesProgress}}" wx:key="id" data-phase="{{item.id}}" bindtap="navigateToPhase">
        <view class="phase-item-header">
          <text class="phase-item-icon">{{item.icon}}</text>
          <view class="phase-item-info">
            <text class="phase-item-title">{{item.title}}</text>
            <text class="phase-item-range">第{{item.startEpisode}}-{{item.endEpisode}}期</text>
          </view>
          <view class="phase-item-status">
            <text class="status-text" wx:if="{{item.status === 'completed'}}">✅ 已完成</text>
            <text class="status-text current" wx:elif="{{item.status === 'current'}}">🚧 进行中</text>
            <text class="status-text pending" wx:else>⏳ 待开始</text>
          </view>
        </view>
        <view class="phase-progress-bar">
          <view class="progress-track">
            <view class="progress-fill" style="width: {{item.progressPercent}}%"></view>
          </view>
          <text class="progress-label">{{item.completedCount}}/{{item.totalCount}} ({{item.progressPercent}}%)</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 最近学习 -->
  <view class="recent-section">
    <text class="section-title">📖 最近学习</text>
    <view class="recent-episodes">
      <view class="recent-episode" wx:for="{{recentEpisodes}}" wx:key="id" data-episode="{{item.id}}" bindtap="navigateToEpisode">
        <view class="episode-number">第{{item.id}}期</view>
        <view class="episode-content">
          <text class="episode-title">{{item.title}}</text>
          <text class="episode-time">{{item.lastViewTime}}</text>
        </view>
        <view class="episode-status">
          <text class="status-icon" wx:if="{{item.completed}}">✅</text>
          <text class="status-icon" wx:else>📖</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <view class="action-buttons">
      <button class="btn btn-primary" bindtap="continueStudy">
        <text class="btn-icon">▶️</text>
        <text class="btn-text">继续学习</text>
      </button>
      <button class="btn btn-secondary" bindtap="resetProgress">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">重置进度</text>
      </button>
    </view>
  </view>
</view>