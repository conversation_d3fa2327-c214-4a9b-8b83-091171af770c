// pages/phase/phase.js
const app = getApp()

Page({
  data: {
    phaseId: '',
    phaseTitle: '',
    startEpisode: 1,
    endEpisode: 64,
    episodeCount: 64,
    showAll: false,
    searchKeyword: '',
    currentFilter: 'all',
    allEpisodes: [],
    filteredEpisodes: [],
    completedCount: 0,
    allPhases: [
      { id: 'preparation', icon: '🔍', title: '前期准备' },
      { id: 'demolition', icon: '🔨', title: '拆改工程' },
      { id: 'electrical', icon: '⚡', title: '水电工程' },
      { id: 'masonry', icon: '🧱', title: '泥瓦工程' },
      { id: 'carpentry', icon: '🪚', title: '木工工程' },
      { id: 'painting', icon: '🎨', title: '油漆工程' },
      { id: 'installation', icon: '🔧', title: '安装工程' },
      { id: 'decoration', icon: '🪴', title: '软装配饰' }
    ]
  },

  onLoad(options) {
    console.log('phase页面onLoad被调用，参数:', options)
    try {
      this.initPageData(options)
      this.loadEpisodesData()
      console.log('phase页面初始化完成')
    } catch (error) {
      console.error('phase页面初始化失败:', error)
    }
  },

  onShow() {
    console.log('phase页面onShow被调用')
    // 检查是否有全局阶段数据需要处理
    if (app.globalData.currentPhase) {
      console.log('发现全局阶段数据:', app.globalData.currentPhase)
      this.initPageDataFromGlobal(app.globalData.currentPhase)
      // 清除全局数据，避免重复使用
      app.globalData.currentPhase = null
    } else if (!this.data.phaseId && !this.data.showAll) {
      // 如果没有特定阶段数据且不是显示全部，则默认显示全部内容
      console.log('默认显示全部内容')
      this.setData({
        showAll: true,
        phaseTitle: '全部内容',
        startEpisode: 1,
        endEpisode: 64,
        episodeCount: 64
      })
      wx.setNavigationBarTitle({
        title: '全部内容 - 施工避坑指南'
      })
      this.loadEpisodesData()
    }
    // 更新收藏状态和进度
    this.updateEpisodesStatus()
  },

  // 从全局数据初始化页面数据
  initPageDataFromGlobal(phaseData) {
    console.log('使用全局数据初始化页面:', phaseData)
    this.setData({
      phaseId: phaseData.phaseId,
      phaseTitle: phaseData.phaseTitle,
      startEpisode: phaseData.startEpisode,
      endEpisode: phaseData.endEpisode,
      episodeCount: phaseData.episodeCount,
      showAll: false
    })

    wx.setNavigationBarTitle({
      title: `${phaseData.phaseTitle} - 施工避坑指南`
    })

    // 重新加载期数数据
    this.loadEpisodesData()
  },

  // 初始化页面数据
  initPageData(options) {
    const showAll = options.showAll === 'true'
    
    if (showAll) {
      this.setData({
        showAll: true,
        phaseTitle: '全部内容',
        startEpisode: 1,
        endEpisode: 64,
        episodeCount: 64
      })
      
      wx.setNavigationBarTitle({
        title: '全部内容 - 施工避坑指南'
      })
    } else {
      const phaseId = options.phaseId || 'preparation'
      const phaseTitle = decodeURIComponent(options.title || '前期准备')
      const startEpisode = parseInt(options.start) || 1
      const endEpisode = parseInt(options.end) || 4
      const episodeCount = endEpisode - startEpisode + 1
      
      this.setData({
        phaseId: phaseId,
        phaseTitle: phaseTitle,
        startEpisode: startEpisode,
        endEpisode: endEpisode,
        episodeCount: episodeCount,
        showAll: false
      })
      
      wx.setNavigationBarTitle({
        title: `${phaseTitle} - 施工避坑指南`
      })
    }
  },

  // 加载期数数据
  loadEpisodesData() {
    const episodes = this.generateEpisodesData()
    this.setData({
      allEpisodes: episodes,
      filteredEpisodes: episodes
    })
    
    this.updateCompletedCount()
  },

  // 生成期数数据
  generateEpisodesData() {
    const episodes = []
    const { startEpisode, endEpisode, showAll } = this.data
    
    const start = showAll ? 1 : startEpisode
    const end = showAll ? 64 : endEpisode
    
    for (let i = start; i <= end; i++) {
      episodes.push(this.getEpisodeData(i))
    }
    
    return episodes
  },

  // 获取单期数据
  getEpisodeData(id) {
    const episodeTemplates = {
      1: { title: '装修前房屋检测避坑指南', description: '墙体裂缝检查、水电管线探测、防水层检测、承重墙识别', category: '前期准备', phase: 'preparation' },
      2: { title: '装修材料进场验收避坑指南', description: '材料质量检查、数量核对、存储要求、验收标准', category: '前期准备', phase: 'preparation' },
      3: { title: '施工队伍选择与合同避坑指南', description: '资质审查、报价对比、合同条款、付款方式', category: '前期准备', phase: 'preparation' },
      4: { title: '施工现场安全防护避坑指南', description: '安全措施、防护用品、应急预案、责任划分', category: '前期准备', phase: 'preparation' },
      5: { title: '承重墙识别与拆改避坑指南', description: '图纸对照、敲击判断、专业检测、安全拆改', category: '拆改工程', phase: 'demolition' },
      6: { title: '非承重墙拆除避坑指南', description: '水电管线确认、保护措施、拆除顺序、垃圾清运', category: '拆改工程', phase: 'demolition' },
      7: { title: '门窗拆除避坑指南', description: '拆除工具准备、测量记录尺寸、成品保护措施、材料回收利用', category: '拆改工程', phase: 'demolition' },
      8: { title: '地面拆除避坑指南', description: '拆除方法选择、结构保护措施、防水层处理、基层清理彻底', category: '拆改工程', phase: 'demolition' },
      9: { title: '吊顶拆除避坑指南', description: '结构类型识别、电线管路处理、龙骨系统拆除、顶面清理修复', category: '拆改工程', phase: 'demolition' },
      10: { title: '厨卫拆除避坑指南', description: '水路断水处理、电路断电保护、洁具拆除顺序、防水层保护', category: '拆改工程', phase: 'demolition' },
      11: { title: '水路改造避坑指南', description: '水路走向设计、管径选择、压力测试、材料用量', category: '水电工程', phase: 'electrical' },
      12: { title: '电路改造避坑指南', description: '回路设计、线缆规格、施工规范、绝缘测试', category: '水电工程', phase: 'electrical' },
      19: { title: '地面找平避坑指南', description: '基层处理、找平材料、施工工艺、验收标准', category: '泥瓦工程', phase: 'masonry' },
      29: { title: '吊顶龙骨安装避坑指南', description: '龙骨选择、安装工艺、验收要点、常见问题', category: '木工工程', phase: 'carpentry' },
      37: { title: '墙面基层处理避坑指南', description: '基层检查、处理方法、材料选择、施工要点', category: '油漆工程', phase: 'painting' },
      47: { title: '开关插座安装避坑指南', description: '位置确定、安装工艺、接线规范、安全检查', category: '安装工程', phase: 'installation' },
      59: { title: '窗帘安装避坑指南', description: '测量方法、安装方式、材料选择、效果调试', category: '软装配饰', phase: 'decoration' }
    }
    
    const template = episodeTemplates[id]
    if (template) {
      return {
        id: id,
        title: template.title,
        description: template.description,
        category: template.category,
        phase: template.phase,
        readingTime: '5分钟阅读',
        progress: this.getEpisodeProgress(id),
        isFavorite: app.isFavorite(id)
      }
    }
    
    // 默认数据
    const phaseMap = {
      1: { category: '前期准备', phase: 'preparation' },
      5: { category: '拆改工程', phase: 'demolition' },
      11: { category: '水电工程', phase: 'electrical' },
      19: { category: '泥瓦工程', phase: 'masonry' },
      29: { category: '木工工程', phase: 'carpentry' },
      37: { category: '油漆工程', phase: 'painting' },
      47: { category: '安装工程', phase: 'installation' },
      59: { category: '软装配饰', phase: 'decoration' }
    }
    
    let category = '其他'
    let phase = 'other'
    
    for (const [start, info] of Object.entries(phaseMap)) {
      if (id >= parseInt(start)) {
        category = info.category
        phase = info.phase
      }
    }
    
    return {
      id: id,
      title: `第${id}期避坑指南`,
      description: '专业装修避坑要点，帮您避免装修陷阱',
      category: category,
      phase: phase,
      readingTime: '5分钟阅读',
      progress: this.getEpisodeProgress(id),
      isFavorite: app.isFavorite(id)
    }
  },

  // 获取期数进度
  getEpisodeProgress(episodeId) {
    const checklistStatus = app.globalData.checklistStatus[episodeId] || {}
    const completedItems = Object.values(checklistStatus).filter(status => status).length
    const totalItems = 10 // 假设每期有10个检查项
    
    return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0
  },

  // 更新期数状态
  updateEpisodesStatus() {
    const episodes = this.data.allEpisodes.map(episode => ({
      ...episode,
      progress: this.getEpisodeProgress(episode.id),
      isFavorite: app.isFavorite(episode.id)
    }))
    
    this.setData({
      allEpisodes: episodes
    })
    
    this.filterEpisodes()
    this.updateCompletedCount()
  },

  // 更新完成数量
  updateCompletedCount() {
    const completedCount = this.data.filteredEpisodes.filter(episode => episode.progress > 0).length
    this.setData({
      completedCount: completedCount
    })
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    
    this.filterEpisodes()
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: ''
    })
    
    this.filterEpisodes()
  },

  // 筛选变更
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      currentFilter: filter
    })
    
    this.filterEpisodes()
  },

  // 筛选期数
  filterEpisodes() {
    let episodes = [...this.data.allEpisodes]
    
    // 按阶段筛选
    if (this.data.currentFilter !== 'all') {
      episodes = episodes.filter(episode => episode.phase === this.data.currentFilter)
    }
    
    // 按关键词搜索
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase()
      episodes = episodes.filter(episode => 
        episode.title.toLowerCase().includes(keyword) ||
        episode.description.toLowerCase().includes(keyword) ||
        episode.category.toLowerCase().includes(keyword)
      )
    }
    
    this.setData({
      filteredEpisodes: episodes
    })
    
    this.updateCompletedCount()
  },

  // 导航到期数页面
  navigateToEpisode(e) {
    const episodeId = e.currentTarget.dataset.episode
    wx.navigateTo({
      url: `/pages/episode/episode?episodeId=${episodeId}`
    })
  },

  // 滚动到顶部
  scrollToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    })
  },

  // 显示随机期数
  showRandomEpisode() {
    const episodes = this.data.filteredEpisodes
    if (episodes.length > 0) {
      const randomIndex = Math.floor(Math.random() * episodes.length)
      const randomEpisode = episodes[randomIndex]
      
      wx.navigateTo({
        url: `/pages/episode/episode?episodeId=${randomEpisode.id}`
      })
    }
  },

  // 分享功能
  onShareAppMessage() {
    const { phaseTitle, showAll } = this.data
    const title = showAll ? '施工避坑指南 - 64期专业装修避坑指南' : `${phaseTitle}阶段 - 施工避坑指南`
    
    return {
      title: title,
      path: `/pages/phase/phase?${showAll ? 'showAll=true' : `phaseId=${this.data.phaseId}&title=${phaseTitle}&start=${this.data.startEpisode}&end=${this.data.endEpisode}`}`,
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
