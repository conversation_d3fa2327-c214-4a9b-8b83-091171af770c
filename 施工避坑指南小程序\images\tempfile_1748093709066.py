from PIL import Image
import os

# 输入和输出路径
input_folder = "D:/Walk in CS/miniprogram/images"
output_folder = "D:/Walk in CS/miniprogram/optimized_images"

# 确保输出目录存在
os.makedirs(output_folder, exist_ok=True)

# 目标尺寸和质量
target_size = (750, 500)
quality = 60

# 需要保留的图片列表
required_images = {
    "xiaofang.jpg": "汕头消防史馆",
    "laomagong.jpg": "老妈宫戏台",
    "lvshejizhi.jpg": "汕头旅社旧址",
    "cunxinshantang.jpg": "汕头存心善堂",
    "shipaotai.jpg": "石炮台公园",
    "chaohaigun.jpg": "潮海关/汕头海关",
    "queshi.jpg": "礐石风景名胜区",
    "mayudao.jpg": "妈屿岛",
    "lianhuafeng.jpg": "莲花峰",
    "xiaogongyuan.jpg": "汕头小公园"
}

# 处理图片
for filename in os.listdir(input_folder):
    if filename.lower() in required_images:
        input_path = os.path.join(input_folder, filename)
        output_path = os.path.join(output_folder, filename.lower())

        try:
            with Image.open(input_path) as img:
                # 调整尺寸
                img = img.resize(target_size)
                
                # 转换为RGB模式（避免透明通道问题）
                if img.mode != "RGB":
                    img = img.convert("RGB")
                
                # 保存为JPG格式并设置质量
                img.save(output_path, "JPEG", quality=quality)
                
                print(f"已优化: {filename}")
        
        except Exception as e:
            print(f"处理失败 {filename}: {e}")

print("优化完成！")
