/* pages/progress/progress.wxss */

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #3a1c71 0%, #d76d77 50%, #ffaf7b 100%);
  color: white;
  padding: 60rpx 32rpx 48rpx;
  text-align: center;
  margin-bottom: 32rpx;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: "";
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.header::after {
  content: "";
  position: absolute;
  bottom: -20rpx;
  left: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
}

.logo {
  position: relative;
  z-index: 2;
}

.logo .title {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 16rpx;
}

.logo .subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  line-height: 1.4;
}

.progress-stats {
  display: flex;
  justify-content: center;
  gap: 64rpx;
  margin-top: 32rpx;
}

.progress-stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 当前阶段 */
.current-phase-section {
  margin-bottom: 32rpx;
  padding: 0 32rpx;
}

.current-phase-card {
  padding: 32rpx;
}

.phase-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.phase-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.phase-info {
  flex: 1;
}

.phase-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1c1e21;
  display: block;
  margin-bottom: 8rpx;
}

.phase-desc {
  font-size: 24rpx;
  color: #65676b;
  display: block;
}

.phase-progress {
  text-align: right;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1877f2;
}

.progress-bar {
  margin-top: 16rpx;
}

.progress-track {
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 阶段进度列表 */
.phases-progress-section {
  margin-bottom: 32rpx;
  padding: 0 32rpx;
}

.phases-list {
  margin-top: 24rpx;
}

.phase-progress-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.phase-progress-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.phase-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.phase-item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.phase-item-info {
  flex: 1;
}

.phase-item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1c1e21;
  display: block;
  margin-bottom: 4rpx;
}

.phase-item-range {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.phase-item-status {
  text-align: right;
}

.status-text {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
  color: #666;
}

.status-text.current {
  background: rgba(24, 119, 242, 0.1);
  color: #1877f2;
}

.phase-progress-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.phase-progress-bar .progress-track {
  flex: 1;
  height: 8rpx;
}

.progress-label {
  font-size: 22rpx;
  color: #666;
  min-width: 120rpx;
  text-align: right;
}

/* 最近学习 */
.recent-section {
  margin-bottom: 32rpx;
  padding: 0 32rpx;
}

.recent-episodes {
  margin-top: 24rpx;
}

.recent-episode {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.recent-episode:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.episode-number {
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 600;
  margin-right: 20rpx;
  min-width: 100rpx;
  text-align: center;
}

.episode-content {
  flex: 1;
}

.episode-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1c1e21;
  display: block;
  margin-bottom: 4rpx;
}

.episode-time {
  font-size: 22rpx;
  color: #999;
  display: block;
}

.episode-status {
  margin-left: 16rpx;
}

.status-icon {
  font-size: 32rpx;
}

/* 操作按钮 */
.actions-section {
  padding: 0 32rpx 32rpx;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.action-buttons .btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 通用样式 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 24rpx;
  padding-left: 16rpx;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  border-radius: 3rpx;
}