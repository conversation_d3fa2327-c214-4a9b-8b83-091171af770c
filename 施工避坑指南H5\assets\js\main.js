// 主要JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initializePage();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 初始化交互功能
    initializeInteractions();
});

// 页面初始化
function initializePage() {
    // 添加页面加载动画
    document.body.classList.add('loaded');
    
    // 检查是否为微信浏览器
    if (isWeChatBrowser()) {
        document.body.classList.add('wechat-browser');
    }
    
    // 初始化统计数据动画
    animateStats();
}

// 绑定事件监听器
function bindEventListeners() {
    // 阶段卡片点击事件
    const phaseCards = document.querySelectorAll('.phase-card');
    phaseCards.forEach(card => {
        card.addEventListener('click', function() {
            const phase = this.dataset.phase;
            navigateToPhase(phase);
        });
    });
    
    // 检查清单交互
    const checkboxes = document.querySelectorAll('.checklist-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('click', function() {
            this.classList.toggle('checked');
            updateProgress();
        });
    });
    
    // 返回顶部功能
    window.addEventListener('scroll', handleScroll);
}

// 初始化交互功能
function initializeInteractions() {
    // 添加触摸反馈
    addTouchFeedback();
    
    // 初始化懒加载
    initializeLazyLoading();
    
    // 初始化分享功能
    initializeSharing();
}

// 检测微信浏览器
function isWeChatBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('micromessenger');
}

// 统计数据动画
function animateStats() {
    const statNumbers = document.querySelectorAll('.stat-item .number');
    
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 30; // 30帧动画
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }
            stat.textContent = Math.floor(currentValue);
        }, 50);
    });
}

// 导航到指定阶段
function navigateToPhase(phase) {
    // 这里可以实现页面跳转或显示对应内容
    console.log('导航到阶段:', phase);
    
    // 示例：滚动到对应内容区域
    const targetSection = document.querySelector(`[data-phase="${phase}"]`);
    if (targetSection) {
        targetSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
        });
    }
}

// 更新进度条
function updateProgress() {
    const checkboxes = document.querySelectorAll('.checklist-checkbox');
    const checkedBoxes = document.querySelectorAll('.checklist-checkbox.checked');
    const progressBars = document.querySelectorAll('.progress-fill');
    
    if (checkboxes.length > 0 && progressBars.length > 0) {
        const progress = (checkedBoxes.length / checkboxes.length) * 100;
        progressBars.forEach(bar => {
            bar.style.width = progress + '%';
            bar.textContent = Math.round(progress) + '%';
        });
    }
}

// 滚动处理
function handleScroll() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    // 显示/隐藏返回顶部按钮
    const backToTop = document.querySelector('.back-to-top');
    if (backToTop) {
        if (scrollTop > 300) {
            backToTop.classList.add('visible');
        } else {
            backToTop.classList.remove('visible');
        }
    }
    
    // 视差滚动效果
    const header = document.querySelector('.header');
    if (header) {
        const offset = scrollTop * 0.5;
        header.style.transform = `translateY(${offset}px)`;
    }
}

// 添加触摸反馈
function addTouchFeedback() {
    const interactiveElements = document.querySelectorAll('.phase-card, .episode-card, .checklist-checkbox');
    
    interactiveElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.classList.add('touching');
        });
        
        element.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('touching');
            }, 150);
        });
    });
}

// 懒加载初始化
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => imageObserver.observe(img));
    }
}

// 分享功能初始化
function initializeSharing() {
    // 微信分享配置（需要微信JS-SDK）
    if (isWeChatBrowser() && typeof wx !== 'undefined') {
        wx.ready(function() {
            const shareData = {
                title: '施工避坑指南 - 装修不踩坑',
                desc: '专业装修知识，避免常见错误，让您的装修更顺利',
                link: window.location.href,
                imgUrl: window.location.origin + '/assets/images/share-icon.jpg'
            };
            
            wx.onMenuShareTimeline(shareData);
            wx.onMenuShareAppMessage(shareData);
        });
    }
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 工具函数：格式化数字
function formatNumber(num) {
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
}

// 工具函数：获取URL参数
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// 错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    // 可以在这里添加错误上报逻辑
});

// 页面卸载时的清理
window.addEventListener('beforeunload', function() {
    // 清理定时器、事件监听器等
});

// 导出主要功能（如果需要在其他文件中使用）
window.SGDGuide = {
    navigateToPhase,
    updateProgress,
    formatNumber,
    getUrlParameter
};
