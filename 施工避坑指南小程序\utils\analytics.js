// utils/analytics.js
// 数据统计分析工具类

class Analytics {
  static KEY_PREFIX = 'analytics_'
  static SESSION_KEY = 'analytics_session'
  static USER_KEY = 'analytics_user'

  /**
   * 初始化统计
   */
  static init() {
    this.initSession()
    this.initUser()
    this.trackAppLaunch()
  }

  /**
   * 初始化会话
   */
  static initSession() {
    const sessionId = this.generateSessionId()
    const sessionData = {
      id: sessionId,
      startTime: Date.now(),
      pageViews: 0,
      events: []
    }
    
    wx.setStorageSync(this.SESSION_KEY, sessionData)
  }

  /**
   * 初始化用户
   */
  static initUser() {
    let userData = wx.getStorageSync(this.USER_KEY)
    
    if (!userData) {
      userData = {
        id: this.generateUserId(),
        firstVisit: Date.now(),
        totalSessions: 0,
        totalPageViews: 0,
        totalEvents: 0
      }
    }
    
    userData.totalSessions += 1
    userData.lastVisit = Date.now()
    
    wx.setStorageSync(this.USER_KEY, userData)
  }

  /**
   * 生成会话ID
   * @returns {string} 会话ID
   */
  static generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成用户ID
   * @returns {string} 用户ID
   */
  static generateUserId() {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 跟踪应用启动
   */
  static trackAppLaunch() {
    const systemInfo = wx.getSystemInfoSync()
    
    this.track('app_launch', {
      platform: systemInfo.platform,
      system: systemInfo.system,
      version: systemInfo.version,
      model: systemInfo.model,
      brand: systemInfo.brand,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      pixelRatio: systemInfo.pixelRatio,
      language: systemInfo.language,
      wifiEnabled: systemInfo.wifiEnabled,
      locationEnabled: systemInfo.locationEnabled,
      bluetoothEnabled: systemInfo.bluetoothEnabled,
      cameraAuthorized: systemInfo.cameraAuthorized,
      locationAuthorized: systemInfo.locationAuthorized,
      microphoneAuthorized: systemInfo.microphoneAuthorized,
      notificationAuthorized: systemInfo.notificationAuthorized
    })
  }

  /**
   * 跟踪页面访问
   * @param {string} pagePath 页面路径
   * @param {object} options 页面参数
   */
  static trackPageView(pagePath, options = {}) {
    // 更新会话数据
    const sessionData = wx.getStorageSync(this.SESSION_KEY) || {}
    sessionData.pageViews = (sessionData.pageViews || 0) + 1
    wx.setStorageSync(this.SESSION_KEY, sessionData)

    // 更新用户数据
    const userData = wx.getStorageSync(this.USER_KEY) || {}
    userData.totalPageViews = (userData.totalPageViews || 0) + 1
    wx.setStorageSync(this.USER_KEY, userData)

    // 记录页面访问事件
    this.track('page_view', {
      page: pagePath,
      options: options,
      timestamp: Date.now(),
      referrer: getCurrentPages().length > 1 ? getCurrentPages()[getCurrentPages().length - 2].route : null
    })
  }

  /**
   * 跟踪自定义事件
   * @param {string} eventName 事件名称
   * @param {object} properties 事件属性
   */
  static track(eventName, properties = {}) {
    try {
      const eventData = {
        event: eventName,
        properties: properties,
        timestamp: Date.now(),
        sessionId: this.getSessionId(),
        userId: this.getUserId()
      }

      // 添加到会话事件列表
      const sessionData = wx.getStorageSync(this.SESSION_KEY) || {}
      if (!sessionData.events) {
        sessionData.events = []
      }
      sessionData.events.push(eventData)
      wx.setStorageSync(this.SESSION_KEY, sessionData)

      // 更新用户事件计数
      const userData = wx.getStorageSync(this.USER_KEY) || {}
      userData.totalEvents = (userData.totalEvents || 0) + 1
      wx.setStorageSync(this.USER_KEY, userData)

      // 存储到日志
      this.saveEventLog(eventData)

      console.log('Analytics Event:', eventData)
    } catch (error) {
      console.error('跟踪事件失败:', error)
    }
  }

  /**
   * 保存事件日志
   * @param {object} eventData 事件数据
   */
  static saveEventLog(eventData) {
    try {
      const today = new Date().toDateString()
      const logKey = `${this.KEY_PREFIX}${today}`
      
      let dayLogs = wx.getStorageSync(logKey) || []
      dayLogs.push(eventData)
      
      // 限制每日日志数量，避免存储过多
      if (dayLogs.length > 1000) {
        dayLogs = dayLogs.slice(-1000)
      }
      
      wx.setStorageSync(logKey, dayLogs)
    } catch (error) {
      console.error('保存事件日志失败:', error)
    }
  }

  /**
   * 获取会话ID
   * @returns {string} 会话ID
   */
  static getSessionId() {
    const sessionData = wx.getStorageSync(this.SESSION_KEY) || {}
    return sessionData.id || 'unknown'
  }

  /**
   * 获取用户ID
   * @returns {string} 用户ID
   */
  static getUserId() {
    const userData = wx.getStorageSync(this.USER_KEY) || {}
    return userData.id || 'unknown'
  }

  /**
   * 获取会话数据
   * @returns {object} 会话数据
   */
  static getSessionData() {
    return wx.getStorageSync(this.SESSION_KEY) || {}
  }

  /**
   * 获取用户数据
   * @returns {object} 用户数据
   */
  static getUserData() {
    return wx.getStorageSync(this.USER_KEY) || {}
  }

  /**
   * 获取指定日期的事件日志
   * @param {string} date 日期字符串
   * @returns {array} 事件日志列表
   */
  static getEventLogs(date = null) {
    const targetDate = date || new Date().toDateString()
    const logKey = `${this.KEY_PREFIX}${targetDate}`
    return wx.getStorageSync(logKey) || []
  }

  /**
   * 获取统计摘要
   * @returns {object} 统计摘要
   */
  static getSummary() {
    const userData = this.getUserData()
    const sessionData = this.getSessionData()
    const todayLogs = this.getEventLogs()

    return {
      user: {
        id: userData.id,
        firstVisit: userData.firstVisit,
        lastVisit: userData.lastVisit,
        totalSessions: userData.totalSessions,
        totalPageViews: userData.totalPageViews,
        totalEvents: userData.totalEvents
      },
      session: {
        id: sessionData.id,
        startTime: sessionData.startTime,
        pageViews: sessionData.pageViews,
        events: sessionData.events ? sessionData.events.length : 0
      },
      today: {
        events: todayLogs.length,
        pageViews: todayLogs.filter(log => log.event === 'page_view').length,
        customEvents: todayLogs.filter(log => log.event !== 'page_view').length
      }
    }
  }

  /**
   * 清理过期日志
   * @param {number} daysToKeep 保留天数
   */
  static cleanupLogs(daysToKeep = 30) {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

      storageInfo.keys.forEach(key => {
        if (key.startsWith(this.KEY_PREFIX)) {
          const dateStr = key.replace(this.KEY_PREFIX, '')
          const logDate = new Date(dateStr)
          
          if (logDate < cutoffDate) {
            wx.removeStorageSync(key)
          }
        }
      })
    } catch (error) {
      console.error('清理日志失败:', error)
    }
  }

  /**
   * 结束会话
   */
  static endSession() {
    const sessionData = this.getSessionData()
    if (sessionData.id) {
      sessionData.endTime = Date.now()
      sessionData.duration = sessionData.endTime - sessionData.startTime
      
      this.track('session_end', {
        sessionId: sessionData.id,
        duration: sessionData.duration,
        pageViews: sessionData.pageViews,
        events: sessionData.events ? sessionData.events.length : 0
      })
    }
  }
}

// 预定义事件类型
const EventTypes = {
  // 页面事件
  PAGE_VIEW: 'page_view',
  PAGE_SHARE: 'page_share',
  
  // 用户行为
  EPISODE_VIEW: 'episode_view',
  EPISODE_COMPLETE: 'episode_complete',
  CHECKLIST_TOGGLE: 'checklist_toggle',
  FAVORITE_ADD: 'favorite_add',
  FAVORITE_REMOVE: 'favorite_remove',
  SEARCH: 'search',
  FILTER: 'filter',
  
  // 功能使用
  SHARE_FRIEND: 'share_friend',
  SHARE_TIMELINE: 'share_timeline',
  COPY_LINK: 'copy_link',
  GENERATE_POSTER: 'generate_poster',
  
  // 应用事件
  APP_LAUNCH: 'app_launch',
  APP_SHOW: 'app_show',
  APP_HIDE: 'app_hide',
  SESSION_END: 'session_end'
}

module.exports = {
  Analytics,
  EventTypes
}
