#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为剩余的episode页面添加返回首页的按钮（处理双引号格式）
"""

import os
import re

def add_home_button_to_episode(file_path):
    """为单个episode文件添加首页按钮"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有首页按钮
        if '🏠 首页' in content:
            print(f"Skip: {os.path.basename(file_path)} - already has home button")
            return True
        
        # 匹配双引号格式的导航
        pattern = r'(<div class="episode-nav">\s*<a href="[^"]*" class="nav-link">← 上一期</a>\s*<span class="episode-indicator">\d+/64</span>\s*)(<a href="[^"]*" class="nav-link">下一期 →</a>\s*</div>)'
        
        # 替换：在期数指示器和下一期按钮之间插入首页按钮
        replacement = r'\1<a href="../index.html" class="nav-link home-link">🏠 首页</a>\n                \2'
        
        new_content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"Success: {os.path.basename(file_path)} - home button added")
            return True
        else:
            print(f"Warning: {os.path.basename(file_path)} - no matching nav structure found")
            return False
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """主函数"""
    episodes_dir = "episodes"
    
    # 需要处理的文件列表（第4-13期）
    episodes_to_fix = [f"episode-{i:02d}.html" for i in range(4, 14)]
    
    success_count = 0
    total_count = len(episodes_to_fix)
    
    for filename in episodes_to_fix:
        file_path = os.path.join(episodes_dir, filename)
        if os.path.exists(file_path):
            if add_home_button_to_episode(file_path):
                success_count += 1
        else:
            print(f"Warning: {filename} does not exist")
    
    print(f"\nProcessing complete: {success_count}/{total_count} files successfully added home button")

if __name__ == "__main__":
    main()
