<!--pages/episode/episode.wxml-->
<view class="container">
  <!-- 头部导航 -->
  <view class="header">
    <view class="logo">
      <text class="title">🏠 施工避坑指南</text>
      <text class="subtitle">第{{episodeData.id}}期：{{episodeData.title}}</text>
    </view>
    <view class="episode-nav">
      <view class="nav-item" bindtap="goHome">
        <text class="nav-icon">🏠</text>
        <text class="nav-text">首页</text>
      </view>
      <view class="episode-indicator">
        <text>{{episodeData.id}}/64</text>
      </view>
      <view class="nav-item" bindtap="goNext" wx:if="{{episodeData.id < 64}}">
        <text class="nav-text">下一期</text>
        <text class="nav-icon">→</text>
      </view>
    </view>
  </view>

  <!-- 内容概览 -->
  <view class="overview-section">
    <view class="overview-card card">
      <text class="section-title">📋 本期要点</text>
      <view class="key-points">
        <view class="point-item" wx:for="{{episodeData.keyPoints}}" wx:key="index">
          <text class="point-icon">{{item.icon}}</text>
          <text class="point-text">{{item.text}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 详细流程 -->
  <view class="content-section">
    <text class="section-title">🔧 详细{{episodeData.processTitle || '操作'}}流程</text>
    <view class="process-flow">
      <view class="flow-step" wx:for="{{episodeData.processSteps}}" wx:key="index">
        <view class="step-number">{{index + 1}}</view>
        <view class="step-content">
          <text class="step-title">{{item.title}}</text>
          <text class="step-desc" wx:for="{{item.descriptions}}" wx:key="desc" wx:for-item="desc">{{desc}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 标准对比 -->
  <view class="standards-section" wx:if="{{episodeData.standards}}">
    <text class="section-title">📏 标准对比</text>
    <view class="standards-table card">
      <view class="table-header">
        <text class="header-cell">检查项目</text>
        <text class="header-cell">国家标准</text>
        <text class="header-cell">常见问题</text>
      </view>
      <view class="table-row" wx:for="{{episodeData.standards}}" wx:key="index">
        <text class="table-cell">{{item.item}}</text>
        <text class="table-cell standard">{{item.standard}}</text>
        <text class="table-cell problem">{{item.problem}}</text>
      </view>
    </view>
  </view>

  <!-- 环境要求 -->
  <view class="environment-section" wx:if="{{episodeData.environment}}">
    <text class="section-title">🌡️ 环境要求</text>
    <view class="environment-card card">
      <view class="env-item" wx:for="{{episodeData.environment}}" wx:key="index">
        <text class="env-label">{{item.label}}：</text>
        <text class="env-value">{{item.value}}</text>
      </view>
    </view>
  </view>

  <!-- 常见瑕疵 -->
  <view class="defects-section" wx:if="{{episodeData.defects}}">
    <text class="section-title">❌ 常见瑕疵</text>
    <view class="defects-list">
      <view class="defect-item card" wx:for="{{episodeData.defects}}" wx:key="index">
        <view class="defect-header">
          <text class="defect-icon">⚠️</text>
          <text class="defect-title">{{item.title}}</text>
        </view>
        <text class="defect-desc">{{item.description}}</text>
        <text class="defect-solution">解决方案：{{item.solution}}</text>
      </view>
    </view>
  </view>

  <!-- 检查清单 -->
  <view class="checklist-section">
    <view class="checklist-header">
      <text class="section-title">✅ 检查清单</text>
      <button class="complete-all-btn" bindtap="completeAllChecklist" wx:if="{{completedCount < episodeData.checklist.length}}" style="width: 114rpx; display: flex; box-sizing: border-box; left: 166rpx; top: -11rpx; position: relative">
        <text class="btn-text">全选</text>
      </button>
    </view>
    <view class="checklist-card card">
      <view class="checklist-item" wx:for="{{episodeData.checklist}}" wx:key="index">
        <view class="checkbox {{checklistStatus[index] ? 'checked' : ''}}" data-index="{{index}}" bindtap="toggleCheckbox">
          <text class="checkbox-icon">{{checklistStatus[index] ? '✓' : ''}}</text>
        </view>
        <text class="checklist-text">{{item}}</text>
      </view>
      <view class="progress-bar">
        <text class="progress-text">完成进度：{{completedCount}}/{{episodeData.checklist.length}}</text>
        <view class="progress-track">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 专家建议 -->
  <view class="advice-section" wx:if="{{episodeData.advice}}">
    <text class="section-title">💡 专家建议</text>
    <view class="advice-card card">
      <text class="advice-text">{{episodeData.advice}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <button class="btn btn-secondary" bindtap="toggleFavorite">
        <text class="btn-icon">{{isFavorite ? '❤️' : '🤍'}}</text>
        <text class="btn-text">{{isFavorite ? '已收藏' : '收藏'}}</text>
      </button>
      <button class="btn btn-primary" open-type="share">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享</text>
      </button>
    </view>
  </view>

  <!-- 导航按钮 -->
  <view class="navigation-section">
    <view class="nav-buttons">
      <button class="nav-btn" bindtap="goPrevious" wx:if="{{episodeData.id > 1}}">
        <text class="nav-btn-icon">←</text>
        <text class="nav-btn-text">上一期</text>
      </button>
      <button class="nav-btn nav-btn-primary" bindtap="goNext" wx:if="{{episodeData.id < 64}}">
        <text class="nav-btn-text">下一期</text>
        <text class="nav-btn-icon">→</text>
      </button>
    </view>
  </view>
</view>
