#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证所有episode页面都有返回首页的按钮
"""

import os
import re

def check_home_button(file_path):
    """检查文件是否有首页按钮"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有首页按钮
        if '🏠 首页' in content and '../index.html' in content:
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return False

def main():
    """主函数"""
    episodes_dir = "episodes"
    
    if not os.path.exists(episodes_dir):
        print(f"Error: Directory {episodes_dir} does not exist")
        return
    
    missing_home_button = []
    total_count = 0
    success_count = 0
    
    # 遍历所有episode文件
    for filename in sorted(os.listdir(episodes_dir)):
        if filename.startswith('episode-') and filename.endswith('.html'):
            file_path = os.path.join(episodes_dir, filename)
            total_count += 1
            
            if check_home_button(file_path):
                success_count += 1
                print(f"OK {filename} - has home button")
            else:
                missing_home_button.append(filename)
                print(f"MISSING {filename} - missing home button")
    
    print(f"\n=== Summary ===")
    print(f"Total files: {total_count}")
    print(f"Files with home button: {success_count}")
    print(f"Files missing home button: {len(missing_home_button)}")
    
    if missing_home_button:
        print(f"\nFiles missing home button:")
        for filename in missing_home_button:
            print(f"  - {filename}")
    else:
        print(f"\nSUCCESS: All episode pages have home buttons!")

if __name__ == "__main__":
    main()
