// pages/progress/progress.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    overallProgress: 0,
    completedEpisodes: 0,
    currentPhase: {},
    phasesProgress: [],
    recentEpisodes: [],
    phases: [
      { id: 'preparation', icon: '🔍', title: '前期准备', startEpisode: 1, endEpisode: 4, episodeCount: 4 },
      { id: 'demolition', icon: '🔨', title: '拆改工程', startEpisode: 5, endEpisode: 10, episodeCount: 6 },
      { id: 'electrical', icon: '⚡', title: '水电工程', startEpisode: 11, endEpisode: 18, episodeCount: 8 },
      { id: 'masonry', icon: '🧱', title: '泥瓦工程', startEpisode: 19, endEpisode: 28, episodeCount: 10 },
      { id: 'carpentry', icon: '🪚', title: '木工工程', startEpisode: 29, endEpisode: 36, episodeCount: 8 },
      { id: 'painting', icon: '🎨', title: '油漆工程', startEpisode: 37, endEpisode: 46, episodeCount: 10 },
      { id: 'installation', icon: '🔧', title: '安装工程', startEpisode: 47, endEpisode: 58, episodeCount: 12 },
      { id: 'decoration', icon: '🪴', title: '软装配饰', startEpisode: 59, endEpisode: 64, episodeCount: 6 }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadProgressData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadProgressData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadProgressData()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的装修进度 - 施工避坑指南',
      path: '/pages/progress/progress',
      imageUrl: '/images/share-cover.jpg'
    }
  },

  // 加载进度数据
  loadProgressData() {
    try {
      const checklistStatus = wx.getStorageSync('checklistStatus') || {}
      const learningProgress = wx.getStorageSync('learningProgress') || {}

      // 计算总体进度
      const totalEpisodes = 64
      let completedEpisodes = 0

      // 统计已完成的期数
      for (let i = 1; i <= totalEpisodes; i++) {
        const episodeStatus = checklistStatus[i] || {}
        const completedItems = Object.values(episodeStatus).filter(status => status).length
        if (completedItems > 0) {
          completedEpisodes++
        }
      }

      const overallProgress = Math.round((completedEpisodes / totalEpisodes) * 100)

      // 计算各阶段进度
      const phasesProgress = this.calculatePhasesProgress(checklistStatus)

      // 获取当前阶段
      const currentPhase = this.getCurrentPhase(phasesProgress)

      // 获取最近学习的期数
      const recentEpisodes = this.getRecentEpisodes(learningProgress)

      this.setData({
        overallProgress: overallProgress,
        completedEpisodes: completedEpisodes,
        currentPhase: currentPhase,
        phasesProgress: phasesProgress,
        recentEpisodes: recentEpisodes
      })

    } catch (error) {
      console.error('加载进度数据失败:', error)
    }
  },

  // 计算各阶段进度
  calculatePhasesProgress(checklistStatus) {
    return this.data.phases.map(phase => {
      let completedCount = 0
      let totalCount = phase.episodeCount

      // 统计该阶段已完成的期数
      for (let i = phase.startEpisode; i <= phase.endEpisode; i++) {
        const episodeStatus = checklistStatus[i] || {}
        const completedItems = Object.values(episodeStatus).filter(status => status).length
        if (completedItems > 0) {
          completedCount++
        }
      }

      const progressPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0

      // 确定阶段状态
      let status = 'pending'
      if (progressPercent === 100) {
        status = 'completed'
      } else if (progressPercent > 0) {
        status = 'current'
      }

      return {
        ...phase,
        completedCount: completedCount,
        totalCount: totalCount,
        progressPercent: progressPercent,
        status: status
      }
    })
  },

  // 获取当前阶段
  getCurrentPhase(phasesProgress) {
    // 找到第一个未完成的阶段作为当前阶段
    const currentPhase = phasesProgress.find(phase => phase.status !== 'completed')
    return currentPhase || phasesProgress[phasesProgress.length - 1]
  },

  // 获取最近学习的期数
  getRecentEpisodes(learningProgress) {
    const recentList = []

    // 获取最近访问的期数（最多5个）
    const sortedProgress = Object.entries(learningProgress)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)

    sortedProgress.forEach(([episodeId, timestamp]) => {
      const id = parseInt(episodeId)
      recentList.push({
        id: id,
        title: `第${id}期装修避坑指南`,
        lastViewTime: this.formatTime(timestamp),
        completed: this.isEpisodeCompleted(id)
      })
    })

    return recentList
  },

  // 检查期数是否完成
  isEpisodeCompleted(episodeId) {
    try {
      const checklistStatus = wx.getStorageSync('checklistStatus') || {}
      const episodeStatus = checklistStatus[episodeId] || {}
      const completedItems = Object.values(episodeStatus).filter(status => status).length
      return completedItems > 0
    } catch (error) {
      return false
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return `${Math.floor(diff / 86400000)}天前`
    }
  },

  // 导航到阶段页面
  navigateToPhase(e) {
    const phaseId = e.currentTarget.dataset.phase
    const phase = this.data.phases.find(p => p.id === phaseId)

    if (phase) {
      // 存储阶段数据到全局
      app.globalData.currentPhase = {
        phaseId: phaseId,
        phaseTitle: phase.title,
        startEpisode: phase.startEpisode,
        endEpisode: phase.endEpisode,
        episodeCount: phase.episodeCount
      }

      // 跳转到阶段页面
      wx.navigateTo({
        url: '/pages/phase/phase'
      })
    }
  },

  // 导航到期数详情
  navigateToEpisode(e) {
    const episodeId = e.currentTarget.dataset.episode
    wx.navigateTo({
      url: `/pages/episode/episode?episodeId=${episodeId}`
    })
  },

  // 继续学习
  continueStudy() {
    // 找到下一个未完成的期数
    const nextEpisode = this.findNextEpisode()
    if (nextEpisode) {
      wx.navigateTo({
        url: `/pages/episode/episode?episodeId=${nextEpisode}`
      })
    } else {
      wx.showToast({
        title: '恭喜！已完成所有内容',
        icon: 'success'
      })
    }
  },

  // 找到下一个未完成的期数
  findNextEpisode() {
    try {
      const checklistStatus = wx.getStorageSync('checklistStatus') || {}

      for (let i = 1; i <= 64; i++) {
        const episodeStatus = checklistStatus[i] || {}
        const completedItems = Object.values(episodeStatus).filter(status => status).length
        if (completedItems === 0) {
          return i
        }
      }

      return null
    } catch (error) {
      return 1
    }
  },

  // 重置进度
  resetProgress() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有学习进度吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('checklistStatus')
            wx.removeStorageSync('learningProgress')
            app.globalData.checklistStatus = {}
            app.globalData.learningProgress = {}

            this.loadProgressData()

            wx.showToast({
              title: '进度已重置',
              icon: 'success'
            })
          } catch (error) {
            wx.showToast({
              title: '重置失败',
              icon: 'error'
            })
          }
        }
      }
    })
  }
})