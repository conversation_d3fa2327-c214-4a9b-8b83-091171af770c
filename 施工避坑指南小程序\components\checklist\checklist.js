// components/checklist/checklist.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 检查清单标题
    title: {
      type: String,
      value: ''
    },
    // 检查清单副标题
    subtitle: {
      type: String,
      value: ''
    },
    // 检查项列表
    items: {
      type: Array,
      value: []
    },
    // 检查状态
    status: {
      type: Object,
      value: {}
    },
    // 是否显示进度条
    showProgress: {
      type: Boolean,
      value: true
    },
    // 是否显示状态图标
    showStatus: {
      type: Boolean,
      value: true
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },
    // 是否启用触觉反馈
    vibration: {
      type: Boolean,
      value: true
    },
    // 是否启用声音反馈
    sound: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    completedCount: 0,
    totalCount: 0,
    progressPercent: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 检查项点击事件
     */
    onItemTap(e) {
      const index = e.currentTarget.dataset.index
      const currentStatus = this.data.status[index] || false
      const newStatus = !currentStatus
      
      // 更新状态
      const updatedStatus = { ...this.data.status }
      updatedStatus[index] = newStatus
      
      this.setData({
        status: updatedStatus
      })
      
      // 更新进度
      this.updateProgress()
      
      // 反馈效果
      this.provideFeedback(newStatus)
      
      // 触发事件
      this.triggerEvent('change', {
        index: index,
        checked: newStatus,
        status: updatedStatus,
        progress: this.data.progressPercent
      })
    },

    /**
     * 全选操作
     */
    onCompleteAll() {
      const newStatus = {}
      this.data.items.forEach((item, index) => {
        newStatus[index] = true
      })
      
      this.setData({
        status: newStatus
      })
      
      this.updateProgress()
      this.provideFeedback(true)
      
      this.triggerEvent('completeAll', {
        status: newStatus,
        progress: 100
      })
    },

    /**
     * 清空操作
     */
    onClearAll() {
      wx.showModal({
        title: '确认清空',
        content: '确定要清空所有已完成的检查项吗？',
        success: (res) => {
          if (res.confirm) {
            const newStatus = {}
            
            this.setData({
              status: newStatus
            })
            
            this.updateProgress()
            this.provideFeedback(false)
            
            this.triggerEvent('clearAll', {
              status: newStatus,
              progress: 0
            })
          }
        }
      })
    },

    /**
     * 更新进度
     */
    updateProgress() {
      const totalCount = this.data.items.length
      const completedCount = Object.values(this.data.status).filter(checked => checked).length
      const progressPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0
      
      this.setData({
        totalCount: totalCount,
        completedCount: completedCount,
        progressPercent: progressPercent
      })
    },

    /**
     * 提供反馈效果
     */
    provideFeedback(isChecked) {
      // 触觉反馈
      if (this.data.vibration) {
        if (isChecked) {
          wx.vibrateShort({
            type: 'light'
          })
        } else {
          wx.vibrateShort({
            type: 'medium'
          })
        }
      }
      
      // 声音反馈
      if (this.data.sound) {
        // 小程序暂不支持自定义音效，可以考虑使用系统提示音
        if (isChecked) {
          wx.showToast({
            title: '已完成',
            icon: 'success',
            duration: 500
          })
        }
      }
    },

    /**
     * 获取完成状态
     */
    getCompletionStatus() {
      return {
        completedCount: this.data.completedCount,
        totalCount: this.data.totalCount,
        progressPercent: this.data.progressPercent,
        isCompleted: this.data.progressPercent === 100,
        status: this.data.status
      }
    },

    /**
     * 设置检查状态
     */
    setCheckStatus(status) {
      this.setData({
        status: status
      })
      this.updateProgress()
    },

    /**
     * 重置所有状态
     */
    reset() {
      this.setData({
        status: {},
        completedCount: 0,
        progressPercent: 0
      })
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
      this.updateProgress()
    },
    
    detached() {
      // 组件实例被从页面节点树移除时执行
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 组件所在的页面被展示时执行
    },
    
    hide() {
      // 组件所在的页面被隐藏时执行
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'items, status': function(items, status) {
      // 当items或status发生变化时，更新进度
      this.updateProgress()
    }
  }
})
