/**app.wxss**/
/* 全局样式 - Meta风格配色 */

/* Meta色彩变量 */
/*
主色：#1877f2 (Meta Blue)
辅助色：#42a5f5 (Light Blue)
背景色：#f0f2f5 (Light Gray)
文字色：#1c1e21 (Dark Gray)
次要文字：#65676b (Medium Gray)
边框色：#e4e6ea (Light Border)
*/

/* 重置样式 */
page {
  background-color: #f0f2f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #1c1e21;
}

/* 容器样式 */
.container {
  padding: 0 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
  background-color: #f0f2f5;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 16rpx;
  overflow: hidden;
  border: 1rpx solid #e4e6ea;
}

.card-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-body {
  padding: 32rpx;
}

.card-footer {
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #f0f0f0;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: #fff;
}

.btn-primary:active {
  opacity: 0.8;
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.btn-secondary:active {
  background: #e9ecef;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
}

/* 图标样式 */
.icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.icon-large {
  width: 64rpx;
  height: 64rpx;
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.badge-primary {
  background: #1877f2;
  color: #fff;
}

.badge-success {
  background: #28a745;
  color: #fff;
}

.badge-warning {
  background: #ffc107;
  color: #333;
}

.badge-danger {
  background: #dc3545;
  color: #fff;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background: #e9ecef;
  margin: 32rpx 0;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx;
  color: #999;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 128rpx 32rpx;
  color: #999;
  text-align: center;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }

.mt-16 { margin-top: 16rpx; }
.mt-32 { margin-top: 32rpx; }
.mb-16 { margin-bottom: 16rpx; }
.mb-32 { margin-bottom: 32rpx; }
.ml-16 { margin-left: 16rpx; }
.mr-16 { margin-right: 16rpx; }

.p-16 { padding: 16rpx; }
.p-32 { padding: 32rpx; }
.pt-16 { padding-top: 16rpx; }
.pb-16 { padding-bottom: 16rpx; }
.pl-16 { padding-left: 16rpx; }
.pr-16 { padding-right: 16rpx; }
