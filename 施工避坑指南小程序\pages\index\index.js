// pages/index/index.js
const app = getApp()

Page({
  data: {
    // 装修阶段数据
    phases: [
      {
        id: 'preparation',
        icon: '🔍',
        title: '前期准备',
        startEpisode: 1,
        endEpisode: 4,
        episodeCount: 4
      },
      {
        id: 'demolition',
        icon: '🔨',
        title: '拆改工程',
        startEpisode: 5,
        endEpisode: 10,
        episodeCount: 6
      },
      {
        id: 'electrical',
        icon: '⚡',
        title: '水电工程',
        startEpisode: 11,
        endEpisode: 18,
        episodeCount: 8
      },
      {
        id: 'masonry',
        icon: '🧱',
        title: '泥瓦工程',
        startEpisode: 19,
        endEpisode: 28,
        episodeCount: 10
      },
      {
        id: 'carpentry',
        icon: '🪚',
        title: '木工工程',
        startEpisode: 29,
        endEpisode: 36,
        episodeCount: 8
      },
      {
        id: 'painting',
        icon: '🎨',
        title: '油漆工程',
        startEpisode: 37,
        endEpisode: 46,
        episodeCount: 10
      },
      {
        id: 'installation',
        icon: '🔧',
        title: '安装工程',
        startEpisode: 47,
        endEpisode: 58,
        episodeCount: 12
      },
      {
        id: 'decoration',
        icon: '🪴',
        title: '软装配饰',
        startEpisode: 59,
        endEpisode: 64,
        episodeCount: 6
      }
    ],
    
    // 快速导航数据
    quickNavItems: [
      {
        id: 1,
        episodeId: 1,
        icon: '🔍',
        title: '从第1期开始',
        description: '房屋检测避坑指南'
      },
      {
        id: 2,
        episodeId: 5,
        icon: '🔨',
        title: '拆改工程',
        description: '承重墙识别与拆改'
      },
      {
        id: 3,
        episodeId: 11,
        icon: '⚡',
        title: '水电改造',
        description: '水路改造避坑指南'
      },
      {
        id: 4,
        episodeId: 19,
        icon: '🧱',
        title: '泥瓦工程',
        description: '地面找平避坑指南'
      },
      {
        id: 5,
        episodeId: 29,
        icon: '🪚',
        title: '木工工程',
        description: '吊顶龙骨安装'
      },
      {
        id: 6,
        episodeId: 37,
        icon: '🎨',
        title: '油漆工程',
        description: '墙面基层处理'
      },
      {
        id: 7,
        episodeId: 47,
        icon: '🔧',
        title: '安装工程',
        description: '开关插座安装'
      },
      {
        id: 8,
        episodeId: 59,
        icon: '🪴',
        title: '软装配饰',
        description: '窗帘安装避坑'
      },
      {
        id: 9,
        episodeId: null,
        icon: '📚',
        title: '查看全部内容',
        description: '浏览全部64期内容',
        isViewAll: true
      }
    ],
    
    // 热门内容数据
    hotEpisodes: [
      {
        id: 5,
        title: '承重墙识别与拆改避坑指南',
        description: '图纸对照、敲击判断、专业检测、安全拆改',
        category: '拆改工程',
        readingTime: '5分钟阅读',
        featured: true
      },
      {
        id: 11,
        title: '水路改造避坑指南',
        description: '水路走向设计、管径选择、压力测试、材料用量',
        category: '水电工程',
        readingTime: '5分钟阅读',
        featured: false
      },
      {
        id: 12,
        title: '电路改造避坑指南',
        description: '回路设计、线缆规格、施工规范、绝缘测试',
        category: '水电工程',
        readingTime: '5分钟阅读',
        featured: false
      },
      {
        id: 1,
        title: '装修前房屋检测避坑指南',
        description: '墙体裂缝检查、水电管线探测、防水层检测、承重墙识别',
        category: '前期准备',
        readingTime: '5分钟阅读',
        featured: false
      }
    ]
  },

  onLoad() {
    // 页面加载时的初始化
    console.log('首页加载，phases数据:', this.data.phases)
    this.initPage()
  },

  onShow() {
    // 页面显示时更新数据
    this.updateUserData()
  },

  // 初始化页面
  initPage() {
    // 从本地存储加载用户数据
    this.loadUserData()
    
    // 统计页面访问
    this.trackPageView()
  },

  // 加载用户数据
  loadUserData() {
    try {
      const learningProgress = wx.getStorageSync('learningProgress') || {}
      const favorites = wx.getStorageSync('favorites') || []
      const checklistStatus = wx.getStorageSync('checklistStatus') || {}
      
      // 更新全局数据
      app.globalData.learningProgress = learningProgress
      app.globalData.favorites = favorites
      app.globalData.checklistStatus = checklistStatus
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  },

  // 更新用户数据
  updateUserData() {
    // 可以在这里更新用户的学习进度等信息
  },

  // 统计页面访问
  trackPageView() {
    // 记录页面访问日志
    console.log('首页访问时间:', new Date().toISOString())
  },

  // 导航到阶段页面
  navigateToPhase(e) {
    console.log('navigateToPhase 被调用', e)

    const phaseId = e.currentTarget.dataset.phase
    console.log('phaseId:', phaseId)
    const phase = this.data.phases.find(p => p.id === phaseId)
    console.log('找到的phase:', phase)

    if (phase) {
      // 由于phase页面是tabBar页面，需要使用switchTab并通过全局数据传递参数
      console.log('设置全局阶段数据:', phase)

      // 将阶段数据存储到全局
      app.globalData.currentPhase = {
        phaseId: phaseId,
        phaseTitle: phase.title,
        startEpisode: phase.startEpisode,
        endEpisode: phase.endEpisode,
        episodeCount: phase.episodeCount
      }

      // 跳转到阶段页面（现在不是tabBar页面了）
      wx.navigateTo({
        url: '/pages/phase/phase',
        success: function(res) {
          console.log('跳转阶段页面成功:', res)
        },
        fail: function(err) {
          console.error('跳转阶段页面失败:', err)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          })
        }
      })
    } else {
      console.error('未找到对应的阶段数据')
    }
  },

  // 导航到具体期数
  navigateToEpisode(e) {
    const episodeId = e.currentTarget.dataset.episode
    
    wx.navigateTo({
      url: `/pages/episode/episode?episodeId=${episodeId}`
    })
  },

  // 导航到全部内容页面
  navigateToAllEpisodes() {
    wx.navigateTo({
      url: '/pages/phase/phase?showAll=true'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '施工避坑指南 - 64期专业装修避坑指南',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '施工避坑指南 - 让装修不踩坑',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
