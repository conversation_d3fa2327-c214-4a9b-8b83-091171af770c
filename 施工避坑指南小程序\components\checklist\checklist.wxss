/* components/checklist/checklist.wxss */

.checklist-container {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 头部 */
.checklist-header {
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.checklist-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.checklist-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 检查项列表 */
.checklist-items {
  margin-bottom: 24rpx;
}

.checklist-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 16rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.checklist-item:last-child {
  margin-bottom: 0;
}

.checklist-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

/* 复选框 */
.checkbox {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-color: #28a745;
}

.checkbox-icon {
  color: white;
  font-size: 24rpx;
  font-weight: 600;
}

/* 内容区域 */
.item-content {
  flex: 1;
  min-width: 0;
}

.item-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 4rpx;
  transition: all 0.3s ease;
}

.item-text.completed {
  color: #666;
  text-decoration: line-through;
}

.item-desc {
  font-size: 22rpx;
  color: #999;
  line-height: 1.3;
  display: block;
}

/* 状态区域 */
.item-status {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}

.status-icon {
  font-size: 24rpx;
}

/* 进度条 */
.checklist-progress {
  margin-bottom: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

.progress-percent {
  font-size: 28rpx;
  font-weight: 600;
  color: #667eea;
}

.progress-bar {
  width: 100%;
}

.progress-track {
  height: 12rpx;
  background: #e9ecef;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

/* 操作按钮 */
.checklist-actions {
  display: flex;
  gap: 16rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.primary:active {
  opacity: 0.8;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.action-btn.secondary:active {
  background: #e9ecef;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 20rpx;
}

.btn-text {
  font-size: 24rpx;
}

/* 动画效果 */
@keyframes checkboxCheck {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.checkbox.checked {
  animation: checkboxCheck 0.3s ease;
}

/* 空状态 */
.checklist-empty {
  text-align: center;
  padding: 64rpx 32rpx;
  color: #999;
}

.empty-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 24rpx;
}
