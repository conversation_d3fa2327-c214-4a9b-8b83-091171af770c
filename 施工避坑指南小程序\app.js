// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        console.log('登录成功', res.code)
      }
    })
  },
  
  onShow() {
    // 小程序启动，或从后台进入前台显示时
    console.log('小程序显示')
  },
  
  onHide() {
    // 小程序从前台进入后台时
    console.log('小程序隐藏')
  },
  
  globalData: {
    userInfo: null,
    // 存储用户的学习进度
    learningProgress: {},
    // 存储用户的收藏
    favorites: [],
    // 存储检查清单的完成状态
    checklistStatus: {},
    // 存储当前选择的阶段信息
    currentPhase: null
  },
  
  // 获取用户学习进度
  getLearningProgress(episodeId) {
    return this.globalData.learningProgress[episodeId] || 0
  },
  
  // 设置用户学习进度
  setLearningProgress(episodeId, progress) {
    this.globalData.learningProgress[episodeId] = progress
    // 同步到本地存储
    wx.setStorageSync('learningProgress', this.globalData.learningProgress)
  },
  
  // 添加收藏
  addFavorite(episodeId, episodeData = null) {
    // 检查是否已经收藏
    const existingIndex = this.globalData.favorites.findIndex(item => item.id === episodeId)
    if (existingIndex > -1) {
      return false
    }

    // 如果没有提供episodeData，创建基本数据
    if (!episodeData) {
      episodeData = this.getBasicEpisodeData(episodeId)
    }

    const favoriteItem = {
      id: episodeId,
      title: episodeData.title || `第${episodeId}期内容`,
      description: episodeData.description || '装修避坑指南内容',
      category: episodeData.category || '装修指南',
      favoriteTime: new Date().toLocaleDateString()
    }

    this.globalData.favorites.push(favoriteItem)
    wx.setStorageSync('favorites', this.globalData.favorites)
    return true
  },

  // 移除收藏
  removeFavorite(episodeId) {
    const index = this.globalData.favorites.findIndex(item => item.id === episodeId)
    if (index > -1) {
      this.globalData.favorites.splice(index, 1)
      wx.setStorageSync('favorites', this.globalData.favorites)
      return true
    }
    return false
  },

  // 检查是否已收藏
  isFavorite(episodeId) {
    return this.globalData.favorites.some(item => item.id === episodeId)
  },

  // 获取基本期数数据
  getBasicEpisodeData(episodeId) {
    // 这里可以根据episodeId返回对应的基本信息
    const categories = ['前期准备', '拆改工程', '水电工程', '泥瓦工程', '木工工程', '油漆工程', '安装工程', '验收工程']
    const categoryIndex = Math.floor((episodeId - 1) / 8)
    const category = categories[categoryIndex] || '装修指南'

    return {
      title: `第${episodeId}期装修避坑指南`,
      description: '专业装修避坑要点，基于国家标准',
      category: category
    }
  }
})
