/* pages/phase/phase.wxss */

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #3a1c71 0%, #d76d77 50%, #ffaf7b 100%);
  color: white;
  padding: 60rpx 32rpx 48rpx;
  text-align: center;
  margin-bottom: 32rpx;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: "";
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.header::after {
  content: "";
  position: absolute;
  bottom: -20rpx;
  left: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
}

.logo {
  position: relative;
  z-index: 2;
}

.logo .title {
  font-size: 36rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.logo .subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.phase-info {
  margin-top: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.phase-range {
  font-size: 24rpx;
  opacity: 0.8;
}

.episode-count {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 搜索栏 */
.search-section {
  margin-bottom: 24rpx;
  padding: 0 32rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #1c1e21;
}

.clear-icon {
  font-size: 28rpx;
  color: #999;
  padding: 8rpx;
}

/* 筛选标签 */
.filter-section {
  margin-bottom: 24rpx;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: flex;
  padding: 0 32rpx;
  gap: 16rpx;
}

.filter-tag {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: white;
  border-radius: 24rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-tag.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.tag-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.tag-text {
  font-size: 24rpx;
}

/* 内容列表 */
.content-section {
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.episode-list {
  margin-bottom: 32rpx;
}

.episode-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.episode-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.episode-number {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.episode-number .number {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.episode-content {
  flex: 1;
}

.episode-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1c1e21;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.episode-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.episode-meta {
  display: flex;
  gap: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
}

.meta-text {
  font-size: 20rpx;
  color: #999;
}

.episode-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.favorite-icon {
  font-size: 24rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #667eea;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 128rpx 32rpx;
}

.empty-icon {
  font-size: 128rpx;
  display: block;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

/* 统计区域 */
.stats-section {
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.stats-card {
  padding: 32rpx;
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 快速操作 */
.quick-actions {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-item {
  display: flex;
  align-items: center;
  background: white;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
}
