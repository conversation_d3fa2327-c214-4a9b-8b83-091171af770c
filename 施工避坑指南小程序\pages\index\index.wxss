/* pages/index/index.wxss */

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #3a1c71 0%, #d76d77 50%, #ffaf7b 100%);
  color: white;
  padding: 60rpx 32rpx 48rpx;
  text-align: center;
  margin-bottom: 32rpx;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: "";
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.header::after {
  content: "";
  position: absolute;
  bottom: -20rpx;
  left: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
}

.logo {
  position: relative;
  z-index: 2;
}

.logo .title {
  font-size: 52rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 20rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 2rpx;
}

.logo .subtitle {
  font-size: 30rpx;
  display: block;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(0, 0, 0, 0.15);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  margin: 0 auto;
  max-width: 90%;
  box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.header-stats {
  display: flex;
  justify-content: center;
  gap: 64rpx;
  margin-top: 32rpx;
}

.header-stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 介绍区域 */
.intro-section {
  margin-bottom: 48rpx;
}

.intro-card {
  padding: 48rpx 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 24rpx;
}

.intro-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 32rpx;
}

.features {
  margin-bottom: 32rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #333;
}

.stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-item .number {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-item .label {
  font-size: 24rpx;
  color: #666;
}

/* 阶段导航 */
.phases-section {
  margin-bottom: 48rpx;
}

.phases-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.phase-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  cursor: pointer;
}

.phase-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.phase-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
  pointer-events: none;
}

.phase-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  pointer-events: none;
}

.phase-range {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
  pointer-events: none;
}

.episode-count {
  font-size: 22rpx;
  color: #999;
  display: block;
  pointer-events: none;
}

/* 快速导航 */
.quick-nav-section {
  margin-bottom: 48rpx;
}

.quick-nav-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.quick-nav-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.quick-nav-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.nav-icon {
  font-size: 40rpx;
  display: block;
  margin-bottom: 16rpx;
}

.nav-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.nav-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 热门内容 */
.latest-section {
  margin-bottom: 48rpx;
}

.episode-list {
  margin-top: 24rpx;
}

.episode-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.episode-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.episode-card.featured {
  border-left: 8rpx solid #1877f2;
}

.episode-number {
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 24rpx;
  min-width: 120rpx;
  text-align: center;
}

.episode-content {
  flex: 1;
}

.episode-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1c1e21;
  display: block;
  margin-bottom: 8rpx;
}

.episode-desc {
  font-size: 24rpx;
  color: #65676b;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.episode-meta {
  display: flex;
  gap: 24rpx;
}

.category {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.reading-time {
  color: #999;
  font-size: 22rpx;
}

.episode-arrow {
  font-size: 32rpx;
  color: #667eea;
  margin-left: 16rpx;
}

/* 全部内容按钮 */
.all-episodes-section {
  text-align: center;
  margin-bottom: 48rpx;
}

.btn {
  width: 100%;
  padding: 32rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
}