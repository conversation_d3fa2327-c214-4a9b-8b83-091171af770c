// utils/share.js
// 分享功能工具类

class ShareUtils {
  /**
   * 生成分享配置
   * @param {object} options 分享选项
   * @returns {object} 分享配置
   */
  static generateShareConfig(options = {}) {
    const {
      title = '施工避坑指南 - 专业装修避坑指南',
      path = '/pages/index/index',
      imageUrl = '/images/share-cover.jpg',
      episodeId = null,
      episodeTitle = null
    } = options

    // 如果是期数分享，自定义标题和路径
    if (episodeId && episodeTitle) {
      return {
        title: `第${episodeId}期：${episodeTitle} - 施工避坑指南`,
        path: `/pages/episode/episode?episodeId=${episodeId}`,
        imageUrl: imageUrl
      }
    }

    return {
      title,
      path,
      imageUrl
    }
  }

  /**
   * 分享到微信好友
   * @param {object} options 分享选项
   * @returns {object} 分享配置
   */
  static shareToFriend(options = {}) {
    const config = this.generateShareConfig(options)
    
    // 记录分享事件
    this.trackShareEvent('friend', config)
    
    return config
  }

  /**
   * 分享到朋友圈
   * @param {object} options 分享选项
   * @returns {object} 分享配置
   */
  static shareToTimeline(options = {}) {
    const config = this.generateShareConfig(options)
    
    // 朋友圈分享只需要标题和图片
    const timelineConfig = {
      title: config.title,
      imageUrl: config.imageUrl
    }
    
    // 记录分享事件
    this.trackShareEvent('timeline', timelineConfig)
    
    return timelineConfig
  }

  /**
   * 复制链接分享
   * @param {object} options 分享选项
   */
  static copyLink(options = {}) {
    const config = this.generateShareConfig(options)
    const shareText = `${config.title}\n\n快来查看这个专业的装修避坑指南！`
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success',
          duration: 2000
        })
        
        // 记录分享事件
        this.trackShareEvent('copy', config)
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error',
          duration: 2000
        })
      }
    })
  }

  /**
   * 生成分享海报
   * @param {object} options 海报选项
   */
  static generatePoster(options = {}) {
    const {
      episodeId = null,
      episodeTitle = null,
      qrCodePath = null
    } = options

    // 这里可以调用海报生成服务
    // 暂时显示提示
    wx.showToast({
      title: '海报生成功能开发中',
      icon: 'none',
      duration: 2000
    })

    // 记录分享事件
    this.trackShareEvent('poster', options)
  }

  /**
   * 记录分享事件
   * @param {string} type 分享类型
   * @param {object} config 分享配置
   */
  static trackShareEvent(type, config) {
    try {
      // 记录分享统计
      const shareLog = {
        type: type,
        title: config.title,
        path: config.path,
        timestamp: new Date().toISOString(),
        userAgent: wx.getSystemInfoSync()
      }
      
      console.log('分享事件:', shareLog)
      
      // 这里可以上报到统计服务
      // Analytics.track('share', shareLog)
      
    } catch (error) {
      console.error('记录分享事件失败:', error)
    }
  }

  /**
   * 显示分享菜单
   * @param {object} options 分享选项
   */
  static showShareMenu(options = {}) {
    const {
      episodeId = null,
      episodeTitle = null,
      showPoster = true,
      showCopy = true
    } = options

    const menuItems = ['分享给朋友', '分享到朋友圈']
    
    if (showCopy) {
      menuItems.push('复制链接')
    }
    
    if (showPoster) {
      menuItems.push('生成海报')
    }

    wx.showActionSheet({
      itemList: menuItems,
      success: (res) => {
        const tapIndex = res.tapIndex
        
        switch (tapIndex) {
          case 0: // 分享给朋友
            // 触发系统分享
            wx.showShareMenu({
              withShareTicket: true,
              menus: ['shareAppMessage']
            })
            break
            
          case 1: // 分享到朋友圈
            wx.showShareMenu({
              withShareTicket: true,
              menus: ['shareTimeline']
            })
            break
            
          case 2: // 复制链接
            if (showCopy) {
              this.copyLink({ episodeId, episodeTitle })
            } else if (showPoster) {
              this.generatePoster({ episodeId, episodeTitle })
            }
            break
            
          case 3: // 生成海报
            if (showPoster) {
              this.generatePoster({ episodeId, episodeTitle })
            }
            break
        }
      }
    })
  }
}

// 分享统计类
class ShareStats {
  static KEY = 'shareStats'

  /**
   * 记录分享统计
   * @param {string} type 分享类型
   * @param {string} target 分享目标
   */
  static record(type, target = '') {
    try {
      const stats = wx.getStorageSync(this.KEY) || {}
      const today = new Date().toDateString()
      
      if (!stats[today]) {
        stats[today] = {}
      }
      
      const key = target ? `${type}_${target}` : type
      stats[today][key] = (stats[today][key] || 0) + 1
      
      wx.setStorageSync(this.KEY, stats)
    } catch (error) {
      console.error('记录分享统计失败:', error)
    }
  }

  /**
   * 获取分享统计
   * @param {string} date 日期字符串
   * @returns {object} 统计数据
   */
  static get(date = null) {
    try {
      const stats = wx.getStorageSync(this.KEY) || {}
      const targetDate = date || new Date().toDateString()
      return stats[targetDate] || {}
    } catch (error) {
      console.error('获取分享统计失败:', error)
      return {}
    }
  }

  /**
   * 获取总分享次数
   * @returns {number} 总分享次数
   */
  static getTotal() {
    try {
      const stats = wx.getStorageSync(this.KEY) || {}
      let total = 0
      
      Object.values(stats).forEach(dayStats => {
        Object.values(dayStats).forEach(count => {
          total += count
        })
      })
      
      return total
    } catch (error) {
      console.error('获取总分享次数失败:', error)
      return 0
    }
  }

  /**
   * 清空分享统计
   */
  static clear() {
    try {
      wx.removeStorageSync(this.KEY)
    } catch (error) {
      console.error('清空分享统计失败:', error)
    }
  }
}

module.exports = {
  ShareUtils,
  ShareStats
}
