<!--pages/favorites/favorites.wxml-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="logo">
      <text class="title">⭐ 我的收藏</text>
      <text class="subtitle">收藏的装修避坑指南内容</text>
    </view>
    <view class="header-stats">
      <view class="header-stat">
        <text class="stat-number">{{favoritesList.length}}</text>
        <text class="stat-label">已收藏</text>
      </view>
      <view class="header-stat">
        <text class="stat-number">64</text>
        <text class="stat-label">总期数</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section" wx:if="{{favoritesList.length > 0}}">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input class="search-input" placeholder="搜索收藏内容..." value="{{searchKeyword}}" bindinput="onSearchInput" />
      <text class="clear-icon" wx:if="{{searchKeyword}}" bindtap="clearSearch">✕</text>
    </view>
  </view>

  <!-- 收藏列表 -->
  <view class="favorites-section" wx:if="{{favoritesList.length > 0}}">
    <view class="episode-list">
      <view class="episode-card" wx:for="{{filteredFavorites}}" wx:key="id" data-episode="{{item.id}}" bindtap="navigateToEpisode">
        <view class="episode-number">第{{item.id}}期</view>
        <view class="episode-content">
          <text class="episode-title">{{item.title}}</text>
          <text class="episode-desc">{{item.description}}</text>
          <view class="episode-meta">
            <text class="category">{{item.category}}</text>
            <text class="favorite-time">{{item.favoriteTime}}</text>
          </view>
        </view>
        <view class="episode-actions">
          <text class="remove-favorite" data-episode="{{item.id}}" bindtap="removeFavorite" catchtap="true">🗑️</text>
          <text class="episode-arrow">→</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{favoritesList.length === 0}}">
    <text class="empty-icon">📚</text>
    <text class="empty-title">还没有收藏内容</text>
    <text class="empty-desc">浏览内容时点击收藏按钮，将喜欢的内容添加到这里</text>
    <view class="empty-actions">
      <view class="btn btn-primary" bindtap="goToIndex">
        <text>去首页看看</text>
      </view>
    </view>
  </view>

  <!-- 搜索无结果 -->
  <view class="no-results" wx:if="{{favoritesList.length > 0 && filteredFavorites.length === 0}}">
    <text class="no-results-icon">🔍</text>
    <text class="no-results-title">没有找到相关内容</text>
    <text class="no-results-desc">试试其他关键词</text>
  </view>
</view>