<!--pages/index/index.wxml-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="logo">
      <text class="title">🏠 施工避坑指南</text>
      <text class="subtitle">一个让您装修不踩坑的小工具</text>
    </view>
    <view class="header-stats">
      <view class="header-stat">
        <text class="stat-number">100%</text>
        <text class="stat-label">永久免费</text>
      </view>
      <view class="header-stat">
        <text class="stat-number">2年</text>
        <text class="stat-label">持续更新</text>
      </view>
    </view>
  </view>

  <!-- 栏目介绍 -->
  <view class="intro-section">
    <view class="intro-card card">
      <text class="section-title">📋 介绍</text>
      <text class="intro-text">共64期内容，涵盖装修全流程的关键避坑要点，帮助业主和施工方避免常见错误，降低返工率。所有内容基于国标GB50210等行业规范，确保专业性和实用性。</text>
      
      <view class="features">
        <view class="feature-item">
          <text class="feature-icon">✅</text>
          <text class="feature-text">基于国标GB50210</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">📱</text>
          <text class="feature-text">进度跟踪</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🎯</text>
          <text class="feature-text">检查清单</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">📊</text>
          <text class="feature-text">简单易懂</text>
        </view>
      </view>

      <view class="stats">
        <view class="stat-item">
          <text class="number">64</text>
          <text class="label">期内容</text>
        </view>
        <view class="stat-item">
          <text class="number">8</text>
          <text class="label">个阶段</text>
        </view>
        <view class="stat-item">
          <text class="number">320+</text>
          <text class="label">个避坑要点</text>
        </view>
        <view class="stat-item">
          <text class="number">640+</text>
          <text class="label">项检查清单</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 阶段导航 -->
  <view class="phases-section">
    <text class="section-title">🔧 装修阶段导航</text>
    <view class="phases-grid">
      <view class="phase-card" wx:for="{{phases}}" wx:key="id" data-phase="{{item.id}}" bindtap="navigateToPhase" data-test="phase-{{item.id}}">
        <view class="phase-icon">{{item.icon}}</view>
        <view class="phase-title">{{item.title}}</view>
        <view class="phase-range">第{{item.startEpisode}}-{{item.endEpisode}}期</view>
        <view class="episode-count">{{item.episodeCount}}期内容</view>
      </view>
    </view>
  </view>

  <!-- 快速导航 -->
  <view class="quick-nav-section">
    <text class="section-title">🚀 快速开始</text>
    <view class="quick-nav-grid">
      <view class="quick-nav-item" wx:for="{{quickNavItems}}" wx:key="id" data-episode="{{item.episodeId}}" bindtap="navigateToEpisode">
        <text class="nav-icon">{{item.icon}}</text>
        <text class="nav-title">{{item.title}}</text>
        <text class="nav-desc">{{item.description}}</text>
      </view>
    </view>
  </view>

  <!-- 热门内容 -->
  <view class="latest-section">
    <text class="section-title">🔥 热门内容</text>
    <view class="episode-list">
      <view class="episode-card {{item.featured ? 'featured' : ''}}" wx:for="{{hotEpisodes}}" wx:key="id" data-episode="{{item.id}}" bindtap="navigateToEpisode">
        <view class="episode-number">第{{item.id}}期</view>
        <view class="episode-content">
          <text class="episode-title">{{item.title}}</text>
          <text class="episode-desc">{{item.description}}</text>
          <view class="episode-meta">
            <text class="category">{{item.category}}</text>
            <text class="reading-time">{{item.readingTime}}</text>
          </view>
        </view>
        <text class="episode-arrow">→</text>
      </view>
    </view>
  </view>

  <!-- 全部内容按钮 -->
  <view class="all-episodes-section">
    <button class="btn btn-primary" bindtap="navigateToAllEpisodes">
      📚 查看全部64期内容
    </button>
  </view>
</view>
