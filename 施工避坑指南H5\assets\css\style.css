/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 16px;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 0;
    text-align: center;
    margin-bottom: 24px;
}

.logo h1 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
}

.subtitle {
    font-size: 16px;
    opacity: 0.9;
}

/* 介绍区域 */
.intro-section {
    margin-bottom: 32px;
}

.intro-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.intro-card h2 {
    font-size: 20px;
    margin-bottom: 16px;
    color: #2c3e50;
}

.intro-card p {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
}

.stats {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
}

.stat-item .number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
}

.stat-item .label {
    font-size: 14px;
    color: #666;
}

/* 阶段导航 */
.phases-section {
    margin-bottom: 32px;
}

.phases-section h2 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #2c3e50;
    text-align: center;
}

.phases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
}

.phase-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.phase-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.phase-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.phase-card h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.phase-card p {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.episode-count {
    display: inline-block;
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 最新内容 */
.latest-section h2 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #2c3e50;
    text-align: center;
}

.episode-list {
    margin-bottom: 32px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.episode-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
}

.episode-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.episode-card.featured {
    border-left: 4px solid #667eea;
}

.episode-number {
    background: #667eea;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    min-width: 60px;
    text-align: center;
}

.episode-content {
    flex: 1;
}

.episode-content h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.episode-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.episode-meta {
    display: flex;
    gap: 12px;
    align-items: center;
}

.category {
    background: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.reading-time {
    font-size: 12px;
    color: #999;
}

.episode-arrow {
    font-size: 18px;
    color: #667eea;
    font-weight: 600;
}

/* 头部统计 */
.header-stats {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.header-stat {
    text-align: center;
}

.header-stat .stat-number {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: white;
}

.header-stat .stat-label {
    font-size: 12px;
    color: rgba(255,255,255,0.8);
}

/* 特色功能 */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin: 20px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.feature-icon {
    font-size: 16px;
}

.feature-text {
    font-size: 14px;
    color: #333;
}

/* 快速导航 */
.quick-nav-section {
    margin-bottom: 32px;
}

.quick-nav-section h2 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #2c3e50;
    text-align: center;
}

.quick-nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.quick-nav-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.quick-nav-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.nav-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.quick-nav-item h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.quick-nav-item p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* 全部内容导航 */
.all-episodes-section {
    margin-bottom: 32px;
}

.all-episodes-section h2 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #2c3e50;
    text-align: center;
}

.episodes-by-phase {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.phase-episodes {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.phase-episodes h3 {
    font-size: 16px;
    margin-bottom: 16px;
    color: #2c3e50;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 8px;
}

.episode-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.episode-link {
    display: block;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    font-size: 14px;
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.episode-link:hover {
    background: #e9ecef;
    transform: translateX(4px);
}

.episode-link.disabled {
    color: #999;
    border-left-color: #ddd;
    cursor: not-allowed;
}

.episode-link.disabled:hover {
    background: #f8f9fa;
    transform: none;
}

/* 底部 */
.footer {
    background: #2c3e50;
    color: white;
    margin-top: 40px;
    padding: 32px 0 16px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
}

.footer-info h3 {
    font-size: 18px;
    margin-bottom: 12px;
    color: white;
}

.footer-info p {
    font-size: 14px;
    color: rgba(255,255,255,0.8);
    margin: 4px 0;
}

.footer-stats {
    display: flex;
    gap: 24px;
}

.footer-stat {
    text-align: center;
}

.footer-stat .stat-number {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #667eea;
}

.footer-stat .stat-label {
    font-size: 12px;
    color: rgba(255,255,255,0.8);
}

.footer-bottom {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.footer-bottom p {
    font-size: 14px;
    color: rgba(255,255,255,0.6);
    margin: 0;
}

/* 单期页面样式 */
.episode-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: rgba(255,255,255,0.3);
}

.nav-link.home-link {
    background: linear-gradient(135deg, #ff6b6b, #ffa500);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.nav-link.home-link:hover {
    background: linear-gradient(135deg, #ff5252, #ff9800);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.episode-indicator {
    color: rgba(255,255,255,0.8);
    font-size: 14px;
}

.overview-section {
    margin-bottom: 32px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.key-points {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.point-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.point-icon {
    font-size: 20px;
}

.content-section {
    margin-bottom: 32px;
}

.content-section h2 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #2c3e50;
}

.navigation-section {
    margin: 40px 0;
}

.nav-buttons {
    display: flex;
    justify-content: space-between;
    gap: 16px;
}

.nav-btn {
    flex: 1;
    padding: 16px 24px;
    text-align: center;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-btn.prev {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e9ecef;
}

.nav-btn.next {
    background: #667eea;
    color: white;
    border: 2px solid #667eea;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 12px;
    }

    .header {
        padding: 20px 0;
    }

    .logo h1 {
        font-size: 24px;
    }

    .episode-nav {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .key-points {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .phases-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .phase-card {
        padding: 16px;
    }

    .phase-icon {
        font-size: 28px;
    }

    .episode-card {
        padding: 16px;
        gap: 12px;
    }

    .episode-content h3 {
        font-size: 15px;
    }

    .episode-content p {
        font-size: 13px;
    }

    .nav-buttons {
        flex-direction: column;
    }

    .header-stats {
        gap: 16px;
    }

    .features {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .quick-nav-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .episode-links {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .footer-stats {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .phases-grid {
        grid-template-columns: 1fr;
    }

    .episode-card {
        flex-direction: column;
        text-align: center;
    }

    .episode-arrow {
        transform: rotate(90deg);
    }

    .header-stats {
        flex-direction: column;
        gap: 12px;
    }

    .quick-nav-grid {
        grid-template-columns: 1fr;
    }

    .footer-stats {
        flex-direction: column;
        gap: 16px;
    }
}
