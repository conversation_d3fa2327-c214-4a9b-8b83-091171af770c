<!--pages/phase/phase.wxml-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="logo">
      <text class="title">🏠 施工避坑指南</text>
      <text class="subtitle" wx:if="{{!showAll}}">{{phaseTitle}}阶段</text>
      <text class="subtitle" wx:else>全部64期内容</text>
    </view>
    <view class="phase-info" wx:if="{{!showAll}}">
      <text class="phase-range">第{{startEpisode}}-{{endEpisode}}期</text>
      <text class="episode-count">共{{episodeCount}}期内容</text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input class="search-input" placeholder="搜索内容..." value="{{searchKeyword}}" bindinput="onSearchInput" />
      <text class="clear-icon" wx:if="{{searchKeyword}}" bindtap="clearSearch">✕</text>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-section" wx:if="{{showAll}}">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-tags">
        <view class="filter-tag {{currentFilter === 'all' ? 'active' : ''}}" data-filter="all" bindtap="onFilterChange">
          <text>全部</text>
        </view>
        <view class="filter-tag {{currentFilter === item.id ? 'active' : ''}}" wx:for="{{allPhases}}" wx:key="id" data-filter="{{item.id}}" bindtap="onFilterChange">
          <text class="tag-icon">{{item.icon}}</text>
          <text class="tag-text">{{item.title}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 内容列表 -->
  <view class="content-section">
    <view class="episode-list">
      <view class="episode-item" wx:for="{{filteredEpisodes}}" wx:key="id" data-episode="{{item.id}}" bindtap="navigateToEpisode">
        <view class="episode-number">
          <text class="number">{{item.id}}</text>
        </view>
        <view class="episode-content">
          <text class="episode-title">{{item.title}}</text>
          <text class="episode-desc">{{item.description}}</text>
          <view class="episode-meta">
            <view class="meta-item">
              <text class="meta-icon">📂</text>
              <text class="meta-text">{{item.category}}</text>
            </view>
            <view class="meta-item">
              <text class="meta-icon">⏱️</text>
              <text class="meta-text">{{item.readingTime}}</text>
            </view>
            <view class="meta-item" wx:if="{{item.progress > 0}}">
              <text class="meta-icon">📊</text>
              <text class="meta-text">{{item.progress}}%</text>
            </view>
          </view>
        </view>
        <view class="episode-status">
          <text class="favorite-icon" wx:if="{{item.isFavorite}}">❤️</text>
          <text class="arrow-icon">→</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredEpisodes.length === 0}}">
      <text class="empty-icon">📭</text>
      <text class="empty-text">没有找到相关内容</text>
      <text class="empty-desc">试试调整搜索关键词或筛选条件</text>
    </view>
  </view>

  <!-- 底部统计 -->
  <view class="stats-section" wx:if="{{!showAll}}">
    <view class="stats-card card">
      <view class="stat-item">
        <text class="stat-number">{{episodeCount}}</text>
        <text class="stat-label">期内容</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{completedCount}}</text>
        <text class="stat-label">已完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{Math.round(completedCount / episodeCount * 100)}}%</text>
        <text class="stat-label">完成率</text>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="scrollToTop">
      <text class="action-icon">⬆️</text>
      <text class="action-text">回到顶部</text>
    </view>
    <view class="action-item" bindtap="showRandomEpisode">
      <text class="action-icon">🎲</text>
      <text class="action-text">随机一期</text>
    </view>
  </view>
</view>
